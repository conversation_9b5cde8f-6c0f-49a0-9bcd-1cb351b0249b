#!/usr/bin/env python3
"""
微信机器人启动脚本
使用前请确保：
1. 已获取微信数据库密钥并填入config.yaml
2. 微信客户端已登录
3. MQTT服务已启动
"""

import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from omni_bot_sdk.bot import Bot
    print("✅ omni-bot-sdk 导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已正确安装 omni-bot-sdk")
    sys.exit(1)

def check_config():
    """检查配置文件"""
    config_path = project_root / "config.yaml"
    if not config_path.exists():
        print("❌ 配置文件 config.yaml 不存在")
        return False
    
    # 简单检查配置文件内容
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
        if 'dbkey: ""' in content:
            print("⚠️  警告: 数据库密钥(dbkey)未设置")
            print("   请先获取微信数据库密钥并填入config.yaml")
            return False
    
    print("✅ 配置文件检查通过")
    return True

def main():
    print("🤖 启动微信机器人...")
    print("=" * 50)
    
    # 检查配置
    if not check_config():
        print("\n请完成配置后重新启动")
        input("按回车键退出...")
        return
    
    try:
        # 创建并启动机器人
        bot = Bot(config_path="config.yaml")
        print("🚀 正在启动机器人...")
        bot.start()
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
