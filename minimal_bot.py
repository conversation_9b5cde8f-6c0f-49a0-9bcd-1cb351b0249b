#!/usr/bin/env python3
"""
精简版微信机器人 - 只提供发送消息功能
"""

import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.abspath("omni-bot-sdk-oss"))

try:
    from omni_bot_sdk.bot import Bot
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保在正确的目录中运行此脚本")
    sys.exit(1)


def main():
    print("🤖 启动精简版微信机器人...")
    print("📋 功能：只提供发送消息API接口")
    print("🔗 API地址：http://127.0.0.1:8000")
    print("=" * 50)
    
    try:
        # 使用精简配置启动机器人
        config_path = "config_minimal.yaml"
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            print("请确保 config_minimal.yaml 文件在当前目录")
            return
        
        print("📝 使用精简配置文件启动...")
        bot = Bot(config_path=config_path)
        
        print("🚀 正在启动机器人...")
        print("⚠️  请确保微信已经登录并保持窗口可见")
        print("💡 启动完成后，您可以通过 http://127.0.0.1:8000 发送消息")
        print("🛑 按 Ctrl+C 停止机器人")
        print("-" * 50)
        
        bot.start()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查微信是否已登录，配置是否正确")


if __name__ == "__main__":
    main()
