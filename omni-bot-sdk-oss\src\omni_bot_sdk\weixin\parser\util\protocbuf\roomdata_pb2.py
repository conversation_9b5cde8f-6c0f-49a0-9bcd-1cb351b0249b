# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: roomdata.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x0eroomdata.proto\x12\x0c\x61pp.protobuf"\x8b\x02\n\x0c\x43hatRoomData\x12:\n\x07members\x18\x01 \x03(\x0b\x32).app.protobuf.ChatRoomData.ChatRoomMember\x12\x0f\n\x07\x66ield_2\x18\x02 \x01(\x05\x12\x0f\n\x07\x66ield_3\x18\x03 \x01(\x05\x12\x0f\n\x07\x66ield_4\x18\x04 \x01(\x05\x12\x15\n\rroom_capacity\x18\x05 \x01(\x05\x12\x0f\n\x07\x66ield_6\x18\x06 \x01(\x05\x12\x0f\n\x07\x66ield_7\x18\x07 \x01(\x03\x12\x0f\n\x07\x66ield_8\x18\x08 \x01(\x03\x1a\x42\n\x0e\x43hatRoomMember\x12\x0c\n\x04wxID\x18\x01 \x01(\t\x12\x13\n\x0b\x64isplayName\x18\x02 \x01(\t\x12\r\n\x05state\x18\x03 \x01(\x05\x62\x06proto3'
)


_CHATROOMDATA = DESCRIPTOR.message_types_by_name["ChatRoomData"]
_CHATROOMDATA_CHATROOMMEMBER = _CHATROOMDATA.nested_types_by_name["ChatRoomMember"]
ChatRoomData = _reflection.GeneratedProtocolMessageType(
    "ChatRoomData",
    (_message.Message,),
    {
        "ChatRoomMember": _reflection.GeneratedProtocolMessageType(
            "ChatRoomMember",
            (_message.Message,),
            {
                "DESCRIPTOR": _CHATROOMDATA_CHATROOMMEMBER,
                "__module__": "roomdata_pb2",
                # @@protoc_insertion_point(class_scope:app.protobuf.ChatRoomData.ChatRoomMember)
            },
        ),
        "DESCRIPTOR": _CHATROOMDATA,
        "__module__": "roomdata_pb2",
        # @@protoc_insertion_point(class_scope:app.protobuf.ChatRoomData)
    },
)
_sym_db.RegisterMessage(ChatRoomData)
_sym_db.RegisterMessage(ChatRoomData.ChatRoomMember)

if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    _CHATROOMDATA._serialized_start = 33
    _CHATROOMDATA._serialized_end = 300
    _CHATROOMDATA_CHATROOMMEMBER._serialized_start = 234
    _CHATROOMDATA_CHATROOMMEMBER._serialized_end = 300
# @@protoc_insertion_point(module_scope)
