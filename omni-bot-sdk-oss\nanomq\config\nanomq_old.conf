# NanoMQ Configuration 0.18.0

##============================================================
## NanoMQ Broker
##============================================================

## url
## Connect with the host and port
##
## Value: nmq-tcp://host:port
url=nmq-tcp://0.0.0.0:1883

## daemon
## Run the NanoMQ broker in a daemon mode
##
## Value: true | false
daemon=false

## num_taskq_thread
## Use a specified number of taskq threads 
##
## Value: 1-255, Obtain automatically if 0
num_taskq_thread=0

## max_taskq_thread
## Use a specified maximunm number of taskq threads
##
## Value: 1-255, Obtain automatically if 0
max_taskq_thread=0

## parallel
## Handle a specified maximum number of outstanding requests
##
## Value: 1-255, Obtain automatically if 0
parallel=0

## Property_size
## The max size for a MQTT user property
##
## Hot updatable
## Value: 1-infinity
property_size=32

## The max packet size of NanoMQ (Kbytes)
## Defines the max size of a packet that NanoMQ could accept
##
## Hot updatable
## Value: 1 KByte-266240 KByte (260 Mb)
max_packet_size=10240

## The default max packet size of each client (Kbytes)
## Defines the default max size limit of sending packet to each client
## Will be overwrite if client set its own max size limit
##
## Hot updatable
## Value: 1 KByte-266240 KByte (260 Mb)
client_max_packet_size=10240

## msq_len
## The queue length in-flight window
## This is essential for performance and memory consumption
##
## Hot updatable
## Value: 1-infinity
msq_len=2048

## qos_duration (s)
## The nano qos duration which also controls timer interval of each pipe
##
## Hot updatable
## Value: 1-infinity
qos_duration=10

## The backoff for MQTT keepalive timeout.
## broker will discolse client when there is no activity for
## 'Keepalive * backoff * timeout.
##
## Hot updatable
## Value: Float > 0.5
keepalive_backoff=1250

## anonymous
## allow anonymous login
##
## Hot updatable
## Value: true | false
allow_anonymous=true

## enable or disable Auth module
##
## Value: true | false
auth_enable=true

## enable internal IPC server
## allow IPC server
##
## This is for CMD parsing & Gateway/Plugins control
## Value: true | false
enable_ipc_internal=false

## enable or disable ACL function
##
## Value: true | false
acl_enable=true

## Allow or deny if no ACL rules matched.
##
## Value: allow | deny
acl_nomatch=allow

## Whether to enable ACL cache.
##
## If enabled, ACLs roles for each client will be cached in the memory
##
## Value: on | off
enable_acl_cache=on

## The maximum count of ACL entries can be cached for a client.
##
## Value: Integer greater than 0
## Default: 32
acl_cache_max_size=32

## The time after which an ACL cache entry will be deleted
##
## Value: Duration
## Default: 1 minute
acl_cache_ttl=1m

## The action when acl check reject current operation
##
## Value: ignore | disconnect
## Default: ignore
acl_deny_action=ignore

## ACL Rules
## Format: 
## acl.rule.<No>=<Spec>

## Allow MQTT client using username "dashboard"  to subscribe to "$SYS/#" topics
# acl.rule.1={"permit": "allow", "username": "dashboard", "action": "subscribe", "topics": ["$SYS/#"]}

## Allow users with IP address "127.0.0.1" to publish/subscribe to topics "$SYS/#", "#"
# acl.rule.2={"permit": "allow", "ipaddr": "127.0.0.1", "action": "pubsub", "topics": ["$SYS/#", "#"]}

## Deny "All Users" subscribe to "$SYS/#" "#" Topics
# acl.rule.3={"permit": "deny", "username": "#", "action": "subscribe", "topics": ["$SYS/#", "#"]}

## Allow any other publish/subscribe operation
# acl.rule.4={"permit": "allow"}

## -------------------- SQLite Config -------------------- ##

## Enable sqlite cache
## Whether to enable sqlite
##
## Value: boolean
sqlite.enable=false

## Max message limitation for caching
## ( 0 means ineffective )
## Value: 1-infinity
sqlite.disk_cache_size=102400

## Mounted file path 
##
## Value: path
#sqlite.mounted_file_path=/tmp/

## The threshold of flushing messages to flash. 
##
## Hot updatable
## Value: 1-infinity
sqlite.flush_mem_threshold=100

## Resend interval (ms)
## The interval for resending the messages after failure recovered. (not related to trigger)
## 
## Value: 1-infinity 
sqlite.resend_interval=5000

##============================================================
## TLS/SSL
##============================================================

## enable tls
## 
## Value: true | false
tls.enable=false

## tls url
##
## Value: "tls+nmq-tcp://host:port"
tls.url=tls+nmq-tcp://0.0.0.0:8883

## tls key password
## String containing the user's password. Only used if the private keyfile
## is password-protected.
## Quote "" is not needed
##
## Value: String
## tls.key_password=yourpass

## tls keyfile
## Path to the file containing the user's private PEM-encoded key.
##
## Value: File
tls.keyfile=/etc/certs/key.pem

## tls cert file
## Path to a file containing the user certificate.
##
## Value: File
tls.certfile=/etc/certs/cert.pem

## tls ca cert file
## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
tls.cacertfile=/etc/certs/cacert.pem

## A server only does x509-path validation in mode verify_peer,
## as it then sends a certificate request to the client (this
## message is not sent if the verify option is verify_none).
## You can then also want to specify option fail_if_no_peer_cert.
##
## Value: true: verify_peer | false: verify_none
tls.verify_peer=false

## Used together with {verify, verify_peer} by an SSL server. If set to true,
## the server fails if the client does not have a certificate to send, that is,
## sends an empty certificate.
##
## Value: true | false
tls.fail_if_no_peer_cert=false


##============================================================
## WebSocket
##============================================================

## allow websocket
##
## Value: true | false
websocket.enable=true

## websocket url
##
## Value: "nmq-ws://host:port/path"
websocket.url=nmq-ws://0.0.0.0:8083/mqtt

## websocket tls url
##
## Value: "nmq-wss://host:port/path"
websocket.tls_url=nmq-wss://0.0.0.0:8084/mqtt


##============================================================
## Http server
##============================================================

## allow http server
##
## Value: true | false
http_server.enable=false

## http server port
##
## Value: 0 - 65535
http_server.port=8081

## parallel for http server
## Handle a specified maximum number of outstanding requests
##
## Value: 1-infinity
http_server.parallel=32

## http server username
##
## Value: String
http_server.username=admin

## http server password
##
## Value: String
http_server.password=public

## http server auth type
## If set auth_type=jwt, make sure you have built JWT dependency with `-DENABLE_JWT=ON` first.
##
## Value: basic | jwt
http_server.auth_type=basic

## http server jwt public key file
## Used together with 'http_server.auth_type=jwt',
## Path to the file containing the user's private key.
##
## Value: File
http_server.jwt.public.keyfile=/etc/certs/jwt/jwtRS256.key.pub

## http server jwt private key file
## Used together with 'http_server.auth_type=jwt',
## Path to the file containing the user's publick key.
##
## Value: File
http_server.jwt.private.keyfile=/etc/certs/jwt/jwtRS256.key



## ------------------ Logging Config ------------------ ##

## Where to emit the logs.
##
## - file: write logs to file
## - console: write logs to standard I/O
## - syslog: write logs to syslog
## Value: file | console | syslog
## Example: file,console,syslog
log.to=file,console

## The log severity level.
##
## Value: trace | debug | info | warn | error | fatal
##
## Note: Only the messages with severity level higher than or equal to
##       this level will be logged.
##
## Default: warn
log.level=info

## The dir for log files.
##
## Value: Folder
log.dir=/tmp

## The log filename for logs of level specified in "log.level".
##
## Value: String
## Default: nanomq.log
log.file=nanomq.log

## Maximum size of each log file.
##
## Value: Number
## Default: 10M
## Supported Unit: KB | MB | GB
log.rotation.size=10MB

## Maximum rotation count of log files.
##
## Value: Number
## Default: 5
log.rotation.count=5

##============================================================
## Authorization 
##============================================================

##auth.1.login = admin
##auth.1.password = public

##auth.2.login = client
##auth.2.password = public


##--------------------------------------------------------------------
## HTTP Authorization
##--------------------------------------------------------------------

## Enable Authentication/ACL with HTTP API
##
## Value: true | false
auth.http.enable = false

## HTTP URL API path for Auth Request
##
## Value: URL
##
## Examples: http://127.0.0.1:80/mqtt/auth, https://[::1]:80/mqtt/auth
auth.http.auth_req.url = http://127.0.0.1:80/mqtt/auth

## HTTP Request Method for Auth Request
##
## Value: post | get
auth.http.auth_req.method = post

## HTTP Request Headers for Auth Request, Content-Type header is configured by default.
## The possible values of the Content-Type header: application/x-www-form-urlencoded, application/json
##
## Examples: auth.http.auth_req.headers.accept = */*
auth.http.auth_req.headers.content-type = application/x-www-form-urlencoded

## Parameters used to construct the request body or query string parameters
## When the request method is GET, these parameters will be converted into query string parameters
## When the request method is POST, the final format is determined by content-type
##
## Available Variables:
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Value: <K1>=<V1>,<K2>=<V2>,...
auth.http.auth_req.params = clientid=%c,username=%u,password=%P

## HTTP URL API path for SuperUser Request
##
## Value: URL
##
## Examples: http://127.0.0.1:80/mqtt/superuser, https://[::1]:80/mqtt/superuser
# auth.http.super_req.url = http://127.0.0.1:80/mqtt/superuser

## Time-out time for the request.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 5s
auth.http.timeout = 5s

## Connection time-out time, used during the initial request,
## when the client is connecting to the server.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 5s
auth.http.connect_timeout = 5s

## Connection process pool size
##
## Value: Number
auth.http.pool_size = 32


##============================================================
## WebHook
##============================================================

## Enable web hook
##
## Value: boolean
web.hook.enable=false

## Webhook URL
##
## Value: String
web.hook.url=http://127.0.0.1:80

## HTTP Headers
##
## Example:
## 1. web.hook.headers.content-type=application/json
## 2. web.hook.headers.accept=*
##
## Value: String
web.hook.headers.content-type=application/json

## The encoding format of the payload field in the HTTP body
## The payload field only appears in the on_message_publish and on_message_delivered actions
##
## Value: plain | base64 | base62
web.hook.body.encoding_of_payload_field=plain

##--------------------------------------------------------------------
## PEM format file of CA's
##
## Value: File
## web.hook.ssl.cacertfile =<PEM format file of CA's>

## Certificate file to use, PEM format assumed
##
## Value: File
## web.hook.ssl.certfile=<Certificate file to use>

## Private key file to use, PEM format assumed
##
## Value: File
## web.hook.ssl.keyfile=<Private key file to use>

## Turn on peer certificate verification
##
## Value: true | false
## web.hook.ssl.verify=false

## If not specified, the server's names returned in server's certificate is validated against
## what's provided `web.hook.url` config's host part.
## Setting to 'false' will make NanoMQ ignore unmatched server names.
## If set with a host name, the server's names returned in server's certificate is validated
## against this value.
##
## Value: true | false
## web.hook.ssl.server_name_indication=false

## Connection process pool size
##
## Value: Number
web.hook.pool_size=32

## Hook Rules
## These configuration items represent a list of events should be forwarded
##
## Format:
##  web.hook.rule.<HookName>.<No>=<Spec>
#web.hook.rule.client.connect.1={"action": "on_client_connect"}
#web.hook.rule.client.connack.1={"action": "on_client_connack"}
#web.hook.rule.client.connected.1={"action": "on_client_connected"}
#web.hook.rule.client.disconnected.1={"action": "on_client_disconnected"}
#web.hook.rule.client.subscribe.1={"action": "on_client_subscribe"}
#web.hook.rule.client.unsubscribe.1={"action": "on_client_unsubscribe"}
#web.hook.rule.session.subscribed.1={"action": "on_session_subscribed"}
#web.hook.rule.session.unsubscribed.1={"action": "on_session_unsubscribed"}
#web.hook.rule.session.terminated.1={"action": "on_session_terminated"}
#web.hook.rule.message.publish.1={"action": "on_message_publish"}
#web.hook.rule.message.delivered.1={"action": "on_message_delivered"}
#web.hook.rule.message.acked.1={"action": "on_message_acked"}


##====================================================================
## MQTT Broker Bridge
##====================================================================

##--------------------------------------------------------------------
## NanoMQ supports multi-channels of MQTT bridging.
##
## Format:
## bridge.mqtt.{name}.{item} = {value}
## Example:
## 1:
## bridge.mqtt.emqx.address = mqtt-tcp://{host}:{port}
## bridge.mqtt.emqx.bridge_mode = true
## bridge.mqtt.emqx.username = admin
## bridge.mqtt.emqx.password = public
## bridge.mqtt.emqx.<item> = {value}
##
## 2:
## bridge.mqtt.aliyun.address = mqtt-tcp://{host}:{port}
## bridge.mqtt.aliyun.bridge_mode = true
## bridge.mqtt.aliyun.username = admin
## bridge.mqtt.aliyun.password = public
## bridge.mqtt.aliyun.<item> = {value}

## Whether to enable bridge mode for mqtt bridge
##
## This option is prepared for the mqtt broker which does not
## support bridge_mode such as the mqtt-plugin of the rabbitmq
##
## Value: boolean
bridge.mqtt.emqx.bridge_mode=false

## Bridge address: host:port .
##
## Value: String
## Example: mqtt-tcp://127.0.0.1:1883
##          tls+mqtt-tcp://127.0.0.1:8883
##          mqtt-quic://************:14567
bridge.mqtt.emqx.address=mqtt-tcp://broker.emqx.io:1883

## Protocol version of the bridge.
##
## Value: Enum
## - 5: mqttv5
## - 4: mqttv311
## - 3: mqttv31
bridge.mqtt.emqx.proto_ver=4

##--------------------------------------------------------------------
## The following config params only effective when set QUIC as the
## transport layer of bridging connection (mqtt-quic://{host}:{port})!
##  

## Ping: interval of a sending keepalive packet via QUIC transport.
##
## Value: Duration
## Default: 120 seconds
bridge.mqtt.emqx.quic_keepalive=120

## Idle Timeout: How long a connection can go idle before it is gracefully shut down.
##  0 to disable timeout, which may lost disconnect event msg.
## Value: Duration
## Default: 120 seconds
bridge.mqtt.emqx.quic_idle_timeout=120

## Disconnect Timeout: How long to wait for an ACK before declaring 
## a path dead and disconnecting, This affects stream living time.
## Value: Duration
## Default: 20 seconds
bridge.mqtt.emqx.quic_discon_timeout=20

## Handshake Timeout: the Max time NanoMQ waits for establishing QUIC connection
## How long a handshake can idle before it is discarded
## Value: Duration
## Default: 60 seconds
bridge.mqtt.emqx.quic_handshake_timeout=60

## Send Idle Timeout: Reset congestion control after being idle `SendIdleTimeout`
## Value: Duration
## Default: 60 seconds
bridge.mqtt.emqx.quic_send_idle_timeout=2

## Initial RTT: Initial RTT estimate. (ms)
## RTT: round trip time
## Default: 800ms
bridge.mqtt.emqx.quic_initial_rtt_ms=800

## Max Ack Delay: How long to wait after receiving data before sending an ACK.
## Value: Duration
## Default: 100ms
bridge.mqtt.emqx.quic_max_ack_delay_ms=100

## congestion_control: pick an algorithm from bbr or cubic
##
## Value: String
## Default: cubic
bridge.mqtt.emqx.quic_congestion_control=cubic

## Hybrid bridging: enable or disable the hybrid bridging mode
## Recommend to enable it when you want to take advantage of QUIC
## but not sure if the public network support QUIC.
## Value: true/false
## Default: false
bridge.mqtt.emqx.hybrid_bridging=false

## multi-stream: enable or disable the multi-stream bridging mode
## Still a feature WIP, but basic func is ready
## Value: true/false
## Default: false
bridge.mqtt.emqx.quic_multi_stream=false

## qos_priority: send QoS 1/2 msg in high prority
## QoS 0 messages remain as same
## Value: true/false
## Default: true
bridge.mqtt.emqx.quic_qos_priority=true

## QUIC 0RTT: enable or diable 0RTT, 0RTT is a feature of QUIC to re-establish
## connection quickly.
## Value: true/false
## Default: true
bridge.mqtt.emqx.quic_0rtt=true

##
##--------------------------------------------------------------------

## The ClientId of a remote bridge.
## Default random string.
##
## Value: String
#bridge.mqtt.emqx.clientid=bridge_client

## Ping: interval of a downward bridge.
##
## Value: Duration
## Default: 10 seconds
bridge.mqtt.emqx.keepalive=60

## The maximum backoff timeout.
## Reconnect after no more than backoff_max when bridge connection lost.
##
## Value: Duration
## Default: 60 seconds
bridge.mqtt.emqx.backoff_max=60

## The Clean start flag of a remote bridge.
##
## Value: boolean
## Default: false
##
## NOTE: Some IoT platforms require clean_start
##       must be set to 'true'
bridge.mqtt.emqx.clean_start=false

# # The transparent proxy flag of the bridging client
# #
# # Value: boolean
# # Default: false
# #
# # NOTE: This option gonna proxy Sub/UnSub action
# # of all local client to this bridging connection as well 
bridge.mqtt.emqx.transparent = false

## The username for a remote bridge.
## Quote "" is not needed
##
## Value: String
bridge.mqtt.emqx.username=username

## The password for a remote bridge.
##
## Value: String
bridge.mqtt.emqx.password=passwd

## Topics that need to be forward to IoTHUB
##
## The topic in msg send to remote will not be changed
## if nothing is filled in after the =
## Like this `bridge.mqtt.emqx.forwards.1.remote_topic=`
##
## Value: String
bridge.mqtt.emqx.forwards.1.remote_topic=fwd/topic1
bridge.mqtt.emqx.forwards.1.local_topic=topic1

## Override flag for retain
## Override the retain flag in the msg is about to forward
## with this option. (0|1) stand for override the retain flag with (0|1).
## 2 stands for keeping retain flag as it is.
## 
## Value: Number (0|1|2)
bridge.mqtt.emqx.forwards.1.retain=2

## Need to subscribe to remote broker topics
##
## The topic in msg send to local will not be changed
## if nothing is filled in after the =
## Like this `bridge.mqtt.emqx.subscription.1.local_topic=`
##
## Value: String
bridge.mqtt.emqx.subscription.1.remote_topic=cmd/topic1
bridge.mqtt.emqx.subscription.1.local_topic=topic1

## Override flag for retain
## Override the retain flag in the msg is about to distribute to clients
## with this option. (0|1) stand for override the retain flag with (0|1).
## 2 stands for keeping retain flag as it is.
## 
## Value: Number (0|1|2)
bridge.mqtt.emqx.subscription.1.retain=2

## Need to subscribe to remote topics QoS.
## Please set QoS for each subscription topic
## otherwise topic is invalid, NanoMQ won't sub to any topic
## Value: Number
bridge.mqtt.emqx.subscription.1.qos=1

## Need to subscribe to remote broker topics
##
## Value: String
bridge.mqtt.emqx.subscription.2.remote_topic=cmd/topic2
bridge.mqtt.emqx.subscription.2.local_topic=topic2

## Override flag for retain
## 
## Value: Number (0|1|2)
bridge.mqtt.emqx.subscription.2.retain=2

## Need to subscribe to remote topics QoS.
##
## Value: Number
bridge.mqtt.emqx.subscription.2.qos=2

## parallel
## Handle a specified maximum number of outstanding requests
##
## Value: 1-infinity
bridge.mqtt.emqx.parallel=2

## max send queue length
## Handle a specified maximum number of message send queue length
##
## Value: 1-infinity
bridge.mqtt.emqx.max_send_queue_len=32

## max receive queue length
## Handle a specified maximum number of message receive queue length
##
## Value: 1-infinity
bridge.mqtt.emqx.max_recv_queue_len=128

# # Resend interval (ms)
# # The interval for resending the messages after failure recovered. (not related to trigger)
# # move from cache to bridge since 0.22.8
# # Value: 1-infinity (uint64)
bridge.mqtt.emqx.resend_interval=5000

# # Resend wait time (ms)
# # The waiting time for resending the messages after it is publiushed.
# # Tips: set it longer than keepalive if you dont want too much duplicated msg
# # Value: 1-infinity (uint64)
bridge.mqtt.emqx.resend_wait = 3000

# # max Ack wait time for each QoS msg (ms)
# # The waiting time for Acknowledgment of every QoS msg. Does not affect normal sending.
# # Tips: QoS msg requries a delivered ack, which occupies an AIO.
# # 	  set a max timeout time to cancel the ack action.
# #       once it is canceled, there is no more retrying of this msg.
# # Value: 1-infinity (uint64)
bridge.mqtt.emqx.cancel_timeout = 10000

## MQTT V5 Property for Connection ## 

# # Maximum Packet Size
# #
# # If the Maximum Packet Size is not present, no limit on the packet size is imposed beyond 
# # the limitations in the protocol as a result of the remaining length encoding and the protocol header sizes.
# # Value: 1 ~ 4294967295
# bridge.mqtt.emqx.connect.properties.maximum_packet_size=0

# # Receive Maximum
# #
# # The Client uses this value to limit the number of QoS 1 and QoS 2 publications that it is willing to process concurrently. 
# # There is no mechanism to limit the QoS 0 publications that the Server might try to send.
# # The value of Receive Maximum applies only to the current Network Connection. 
# # If the Receive Maximum value is absent then its value defaults to 65,535.
# #
# # Value: 1 ~ 65535
# bridge.mqtt.emqx.connect.properties.receive_maximum=65535

# # Topic Alias Maximum
# #
# # If the Topic Alias Maximum property is absent, the default value is 0.
# # This value indicates the highest value that the Client will accept as a Topic Alias sent by the Server. 
# # The Client uses this value to limit the number of Topic Aliases that it is willing to hold on this Connection.
# # Default: 0
# # Value: 0 ~ 65535
# bridge.mqtt.emqx.connect.properties.topic_alias_maximum=0

# # Request Problem Information
# #
# # If the Request Problem Information is absent, the value of 1 is used.
# # The Client uses this value to indicate whether the Reason String or User Properties are sent in the case of failures.
# # If the value of Request Problem Information is 0, the Server MAY return a Reason String or User Properties on a CONNACK or DISCONNECT packet, 
# # but MUST NOT send a Reason String or User Properties on any packet other than PUBLISH, CONNACK, or DISCONNECT. 
# # If the value is 0 and the Client receives a Reason String or User Properties in a packet other than PUBLISH, CONNACK, or DISCONNECT, 
# # it uses a DISCONNECT packet with Reason Code 0x82 (Protocol Error) as described in section 4.13 Handling errors.
# # If this value is 1, the Server MAY return a Reason String or User Properties on any packet where it is allowed.
# # 
# # Default: 1
# # Value: 0 | 1
# bridge.mqtt.emqx.connect.properties.request_problem_infomation=0

# # Request Response Information
# #
# # If the Request Response Information is absent, the value of 0 is used.
# # The Client uses this value to request the Server to return Response Information in the CONNACK. 
# # A value of 0 indicates that the Server MUST NOT return Response Information. 
# # If the value is 1 the Server MAY return Response Information in the CONNACK packet.
# #
# # Default: 0
# # Value: 0 | 1
# bridge.mqtt.emqx.connect.properties.request_response_infomation=1

# # Session Expiry Interval
# #
# # If the Session Expiry Interval is absent the value 0 is used. 
# # If it is set to 0, or is absent, the Session ends when the Network Connection is closed.
# # If the Session Expiry Interval is 4294967295 (UINT_MAX), the Session does not expire.
# #
# # Value: 0 ~ 4294967295
# bridge.mqtt.emqx.connect.properties.session_expiry_interval=0

# # User Property
# #
# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
# # The same name is allowed to appear more than once.
# # Value: Json string: key(String) - value(String)
# bridge.mqtt.emqx.connect.properties.user_property={"key1":"value1","key2":"value2"}

## MQTT V5 Property for Subscription ## 

# # Subscription Identifier
# #
# # The Subscription Identifier can have the value of 1 to 268,435,455.
# # It is a Protocol Error if the Subscription Identifier has a value of 0. 
# # It is a Protocol Error to include the Subscription Identifier more than once.
# # The Subscription Identifier is associated with any subscription created or modified as the result of this SUBSCRIBE packet. 
# # If there is a Subscription Identifier, it is stored with the subscription. 
# # If this property is not specified, then the absence of a Subscription Identifier is stored with the subscription.
# #
# # Value: 1 ~ 268,435,455
# bridge.mqtt.emqx.subscription.properties.identifier=1

# # User Property
# #
# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
# # The same name is allowed to appear more than once.
# # Value: Json string: key(String) - value(String)
# bridge.mqtt.emqx.subscription.properties.user_property={"key1":"value1","key2":"value2"}

# # Will topic
bridge.mqtt.emqx.will.topic=will_topic

# # Will qos
bridge.mqtt.emqx.will.qos=2

# # Will retain
bridge.mqtt.emqx.will.retain=false 

# # Will payload
bridge.mqtt.emqx.will.payload=will_message

## MQTT V5 Property for Will ##

# # Payload Format Indicator
# # 
# # 0 (0x00) Byte Indicates that the Will Message is unspecified bytes, 
# # which is equivalent to not sending a Payload Format Indicator.
# #
# # 1 (0x01) Byte Indicates that the Will Message is UTF-8 Encoded Character Data.
# #
# # Default: 0
# # Value: 0 | 1
bridge.mqtt.emqx.will.properties.payload_format_indicator=0

# # Message Expiry Interval
# #
# # If present, the Four Byte value is the lifetime of the Will Message in seconds 
# # and is sent as the Publication Expiry Interval when the Server publishes the Will Message.
# # 
# # If absent, no Message Expiry Interval is sent when the Server publishes the Will Message.
bridge.mqtt.emqx.will.properties.message_expiry_interval=100

# # Content Type
# # The value of the Content Type is defined by the sending and receiving application.
# bridge.mqtt.emqx.will.properties.content_type=

# # Response Topic
# # The presence of a Response Topic identifies the Will Message as a Request.
# bridge.mqtt.emqx.will.properties.response_topic=

# # Correlation Data
# # The Correlation Data is used by the sender of the Request Message to identify which request 
# # the Response Message is for when it is received.
# bridge.mqtt.emqx.will.properties.correlation_data=

# # Will Delay Interval
# # 
# # If the Will Delay Interval is absent, the default value is 0 and there is no delay 
# # before the Will Message is published.
# #
# # The Server delays publishing the Client’s Will Message until the Will Delay Interval 
# # has passed or the Session ends, whichever happens first. 
# # If a new Network Connection to this Session is made before the Will Delay Interval has passed, the Server MUST NOT send the Will Message
bridge.mqtt.emqx.will.properties.will_delay_interval=30

# # User Property
# #
# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
# # The same name is allowed to appear more than once.
# # Value: Map[key(String) - value(String)]
bridge.mqtt.emqx.will.properties.user_property={"key1":"value1","key2":"value2"}


## enable tls
## 
## Value: true | false
bridge.mqtt.emqx.tls.enable=false

## tls key password
## String containing the user's password. Only used if the private keyfile
## is password-protected.
##
## Value: String
## bridge.mqtt.emqx.tls.key_password=yourpass

## tls keyfile
## Path of the file containing the client's private key.
##
## Value: File
## bridge.mqtt.emqx.tls.keyfile=/etc/certs/key.pem

## tls cert file
## Path of the file containing the client certificate.
##
## Value: File
## bridge.mqtt.emqx.tls.certfile=/etc/certs/cert.pem

## tls ca cert file
## Path of the file containing the server's root CA certificate.  
## 
## This certificate is used to identify the Emqx IoT server and is publicly
## available.
##
## Value: File
## bridge.mqtt.emqx.tls.cacertfile=/etc/certs/cacert.pem

##--------------------------------------------------------------------
## Next bridge configuration
## 
## bridge.mqtt.<name>.address=mqtt-tcp://{host}:{port}
## bridge.mqtt.<name>.bridge_mode=true
## bridge.mqtt.<name>.username=admin
## bridge.mqtt.<name>.password=public
## bridge.mqtt.<name>.<item>={value}
## 
##--------------------------------------------------------------------

## SQLite Config ##

## Enable sqlite cache
## Whether to enable sqlite
##
## Value: boolean
bridge.sqlite.enable=false

## Max message limitation for caching
## ( 0 means ineffective )
## Value: 1-infinity
bridge.sqlite.disk_cache_size=102400

## Mounted file path
##
## Value: path
#bridge.sqlite.mounted_file_path=/tmp/

## The threshold of flushing messages to flash.
##
## Value: 1-infinity
bridge.sqlite.flush_mem_threshold=100

##====================================================================
## AWS IoT Core Bridge
##====================================================================

##--------------------------------------------------------------------
## NanoMQ supports multiple aws bridging.
##
## Format:
## aws.bridge.mqtt.{name}.{item} = {value}

## aws endpoint
##
## Value: String
aws.bridge.mqtt.aws.host=localhost

## aws mqtt port
##
## Value: String
aws.bridge.mqtt.aws.port=8883

## Protocol version of the bridge.
##
## Value: Enum
## - 5: mqttv5
## - 4: mqttv311
aws.bridge.mqtt.aws.proto_ver=4

## Whether to enable bridge mode for mqtt bridge
##
## This option is prepared for the mqtt broker which does not
## support bridge_mode such as the mqtt-plugin of the rabbitmq
##
## Value: boolean
aws.bridge.mqtt.aws.bridge_mode=false

## The ClientId of a remote bridge.
## Default random string.
##
## Value: String
aws.bridge.mqtt.aws.clientid=aws_bridge_client

## Ping interval of a down bridge.
##
## Value: Duration
## Default: 10 seconds
aws.bridge.mqtt.aws.keepalive=60

## The Clean start flag of a remote bridge.
##
## Value: boolean
## Default: true
##
## NOTE: Some IoT platforms require clean_start
##       must be set to 'true'
aws.bridge.mqtt.aws.clean_start=true

## The username for a remote bridge.
##
## Value: String
# aws.bridge.mqtt.aws.username=username

## The password for a remote bridge.
##
## Value: String
# aws.bridge.mqtt.aws.password=passwd

## Topics that need to be forward to IoTHUB
##
## Value: String
aws.bridge.mqtt.aws.forwards.1.remote_topic=fwd/topic1
aws.bridge.mqtt.aws.forwards.1.local_topic=topic1

## Override flag for retain
## 
## Value: Number (0|1|2)
aws.bridge.mqtt.aws.forwards.1.retain=2

## Need to subscribe to remote broker topics
##
## Value: String
aws.bridge.mqtt.aws.subscription.1.remote_topic=cmd/topic1
aws.bridge.mqtt.aws.subscription.1.local_topic=topic1

## Override flag for retain
## 
## Value: Number (0|1|2)
aws.bridge.mqtt.aws.subscription.1.retain=2

## Need to subscribe to remote topics QoS.
##
## Value: Number
aws..mqtt.aws.subscription.1.qos=1

## Need to subscribe to remote broker topics
##
## Value: String
aws.bridge.mqtt.aws.subscription.2.remote_topic=cmd/topic2
aws.bridge.mqtt.aws.subscription.2.local_topic=topic2

## Need to subscribe to remote topics QoS.
##
## Value: Number
aws.bridge.mqtt.aws.subscription.2.qos=2

## parallel
## Handle a specified maximum number of outstanding requests
##
## Value: 1-infinity
aws.bridge.mqtt.aws.parallel=2

## tls config ##

## enable tls
## 
## Value: true | false
aws.bridge.mqtt.aws.tls.enable=true

## tls key password
## String containing the user's password. Only used if the private keyfile
## is password-protected.
##
## Value: String
## aws.bridge.mqtt.aws.tls.key_password=yourpass

## tls keyfile
## Path of the file containing the client's private key.
##
## Value: File
aws.bridge.mqtt.aws.tls.keyfile=/etc/certs/key.pem

## tls cert file
## Path of the file containing the client certificate.
##
## Value: File
aws.bridge.mqtt.aws.tls.certfile=/etc/certs/cert.pem

## tls ca cert file
## Path of the file containing the server's root CA certificate.  
## 
## This certificate is used to identify the AWS IoT server and is publicly
## available.
##
## Value: File
aws.bridge.mqtt.aws.tls.cacertfile=/etc/certs/cacert.pem

## A server only does x509-path validation in mode verify_peer,
## as it then sends a certificate request to the client (this
## message is not sent if the verify option is verify_none).
## You can then also want to specify option fail_if_no_peer_cert.
##
## Value: true: verify_peer | false: verify_none
aws.bridge.mqtt.aws.tls.verify_peer=false

## Used together with {verify, verify_peer} by an SSL server. If set to true,
## the server fails if the client does not have a certificate to send, that is,
## sends an empty certificate.
##
## Value: true | false
aws.bridge.mqtt.aws.tls.fail_if_no_peer_cert=false


##============================================================
## MQTT Rule Engine
##============================================================

## Rule engine option, when persistence with 
## rule engine, this option is must be ON.
## 
## Value: ON | OFF
rule_option=ON

## Rule engine plugins option
## Choose a plugin to enable
## 
## Value: enable/disable
rule_option.sqlite=enable

## Rule engine plugins option
## Choose a plugin to enable
## 
## Value: enable/disable
rule_option.repub=enable

## Rule engine plugins option
## Choose a plugin to enable
## 
## Value: enable/disable
rule_option.mysql=enable


##====================================================================
## MQTT Rule Engine for Sqlite
##====================================================================

## Hot updatable
## Rule engine option SQLite3 database path
## Rule engine db path, default is exec path.
## 
## Value: File
rule.sqlite.path=/tmp/sqlite_rule.db

## Rule engine option SQLite3 database table name
## Rule engine db table name.
## 
## Value: String
rule.sqlite.1.table=broker

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.sqlite.event.publish.1.sql="SELECT payload.x.y as y, payload.z as z FROM "#" WHERE y > 10 and z != 'str'"

## Rule engine option SQLite3 database table name
## Rule engine db table name.
## 
## Value: String
rule.sqlite.2.table=broker1

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.sqlite.event.publish.2.sql="SELECT * FROM "abc""

## Rule engine option SQLite3 database table name
## Rule engine db table name.
## 
## Value: String
rule.sqlite.3.table=broker2

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.sqlite.event.publish.3.sql="SELECT payload, qos FROM "#" WHERE qos > 0"


##====================================================================
## MQTT Rule Engine for Repub
##====================================================================

## Repub address: host:port .
##
## Value: String
## Example: mqtt-tcp://127.0.0.1:1883
rule.repub.1.address=mqtt-tcp://localhost:1883

## Repub topic .
##
## Value: String
## Example: topic/repub
rule.repub.1.topic=topic/repub1

## Protocol version of the Repub.
##
## Value: Enum
## - 5: mqttv5
## - 4: mqttv311
## - 3: mqttv31
rule.repub.1.proto_ver=4

## The ClientId of a Repub client.
## Default random string.
##
## Value: String
rule.repub.1.clientid=repub_client1

## Ping interval of a Repub client.
##
## Value: Duration
## Default: 10 seconds
rule.repub.1.keepalive=60

## The Clean start flag of a Repub client.
##
## Value: boolean
## Default: true
##
## NOTE: Some IoT platforms require clean_start
##       must be set to 'true'
rule.repub.1.clean_start=true

## The username for a Repub client.
##
## Value: String
rule.repub.1.username=username

## The password for a Repub.
##
## Value: String
rule.repub.1.password=passwd


## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.repub.event.publish.1.sql="SELECT payload.x.y as y, payload.z as z FROM "#" WHERE y > 10 and z != 'str'"


## Repub address: host:port .
##
## Value: String
## Example: mqtt-tcp://127.0.0.1:1883
rule.repub.2.address=mqtt-tcp://localhost:1885

## Repub topic .
##
## Value: String
## Example: topic/repub
rule.repub.2.topic=topic/repub2

## Protocol version of the Repub.
##
## Value: Enum
## - 5: mqttv5
## - 4: mqttv311
## - 3: mqttv31
rule.repub.2.proto_ver=4

## The ClientId of a Repub client.
## Default random string.
##
## Value: String
rule.repub.2.clientid=repub_client2

## Ping interval of a Repub client.
##
## Value: Duration
## Default: 10 seconds
rule.repub.2.keepalive=60

## The Clean start flag of a Repub client.
##
## Value: boolean
## Default: true
##
## NOTE: Some IoT platforms require clean_start
##       must be set to 'true'
rule.repub.2.clean_start=true

## The username for a Repub client.
##
## Value: String
rule.repub.2.username=username

## The password for a Repub.
##
## Value: String
rule.repub.2.password=passwd

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.repub.event.publish.2.sql="SELECT topic, payload FROM "abc""


## Repub address: host:port .
##
## Value: String
## Example: mqtt-tcp://127.0.0.1:1883
rule.repub.3.address=mqtt-tcp://broker.emqx.io:1883

## Repub topic .
##
## Value: String
## Example: topic/repub
rule.repub.3.topic=topic/repub3

## Protocol version of the Repub.
##
## Value: Enum
## - 5: mqttv5
## - 4: mqttv311
## - 3: mqttv31
rule.repub.3.proto_ver=4

## The ClientId of a Repub client.
## Default random string.
##
## Value: String
rule.repub.3.clientid=repub_client3

## Ping interval of a Repub client.
##
## Value: Duration
## Default: 10 seconds
rule.repub.3.keepalive=60

## The Clean start flag of a Repub client.
##
## Value: boolean
## Default: true
##
## NOTE: Some IoT platforms require clean_start
##       must be set to 'true'
rule.repub.3.clean_start=true

## The username for a Repub client.
##
## Value: String
rule.repub.3.username=username

## The password for a Repub.
##
## Value: String
rule.repub.3.password=passwd

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.repub.event.publish.3.sql="SELECT * FROM "#" "


##====================================================================
## MQTT Rule Engine for Mysql
##====================================================================

## Rule engine option mysql database name
## Rule engine db path, default is exec path.
## 
## Value: File
rule.mysql.name=mysql_rule.db

## Rule engine option mysql database table name
## Rule engine db table name.
## 
## Value: String
rule.mysql.1.table=broker

## The host for a mqsql client.
##
## Value: String
rule.mysql.1.host=localhost

## The username for a mqsql client.
##
## Value: String
rule.mysql.1.username=username

## The password for a Repub.
##
## Value: String
rule.mysql.1.password=password

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.mysql.event.publish.1.sql="SELECT payload.x.y as y, payload.z as z FROM "#" WHERE y > 10 and z != 'str'"

## Rule engine option mysql database table name
## Rule engine db table name.
## 
## Value: String
rule.mysql.2.table=broker1

## The host for a mqsql client.
##
## Value: String
rule.mysql.2.host=localhost

## The username for a mqsql client.
##
## Value: String
rule.mysql.2.username=username

## The password for a Repub.
##
## Value: String
rule.mysql.2.password=password

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.mysql.event.publish.2.sql="SELECT * FROM "abc""

## Rule engine option mysql database table name
## Rule engine db table name.
## 
## Value: String
rule.mysql.3.table=broker2

## The host for a mqsql client.
##
## Value: String
rule.mysql.3.host=localhost

## The username for a mqsql client.
##
## Value: String
rule.mysql.3.username=username

## The password for a Repub.
##
## Value: String
rule.mysql.3.password=password

## Rule engine option sql
## Rule engine sql clause.
## 
## Value: String
rule.mysql.event.publish.3.sql="SELECT payload, qos FROM "#" WHERE qos > 0"


