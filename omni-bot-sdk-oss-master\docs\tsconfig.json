{"compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "lib": ["DOM", "ES2020"], "module": "ESNext", "moduleResolution": "node", "jsx": "preserve", "newLine": "lf", "noEmitOnError": true, "noImplicitAny": false, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictNullChecks": true, "target": "ES2018"}, "include": ["./.vitepress/**/*", "./.vitepress/env.d.ts"]}