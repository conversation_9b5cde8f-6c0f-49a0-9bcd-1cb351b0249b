LICENSE
MANIFEST.in
README.md
config.example.yaml
pyproject.toml
examples/simple-bot/bot.py
scripts/update_version_and_tag.py
src/omni_bot_sdk/__init__.py
src/omni_bot_sdk/bot.py
src/omni_bot_sdk/models.py
src/omni_bot_sdk.egg-info/PKG-INFO
src/omni_bot_sdk.egg-info/SOURCES.txt
src/omni_bot_sdk.egg-info/dependency_links.txt
src/omni_bot_sdk.egg-info/entry_points.txt
src/omni_bot_sdk.egg-info/requires.txt
src/omni_bot_sdk.egg-info/top_level.txt
src/omni_bot_sdk/clients/dify_client.py
src/omni_bot_sdk/clients/minio_client.py
src/omni_bot_sdk/clients/mqtt_client.py
src/omni_bot_sdk/common/__init__.py
src/omni_bot_sdk/common/config.py
src/omni_bot_sdk/common/exceptions.py
src/omni_bot_sdk/common/queues.py
src/omni_bot_sdk/mcp/__init__.py
src/omni_bot_sdk/mcp/app.py
src/omni_bot_sdk/mcp/dispatchers.py
src/omni_bot_sdk/mcp/protocols.py
src/omni_bot_sdk/plugins/__init__.py
src/omni_bot_sdk/plugins/interface.py
src/omni_bot_sdk/plugins/plugin_manager.py
src/omni_bot_sdk/plugins/core/__init__.py
src/omni_bot_sdk/plugins/core/block_empty_room_plugin.py
src/omni_bot_sdk/plugins/core/plugin_interface.py
src/omni_bot_sdk/plugins/core/self_msg_plugin.py
src/omni_bot_sdk/rpa/__init__.py
src/omni_bot_sdk/rpa/controller.py
src/omni_bot_sdk/rpa/image_processor.py
src/omni_bot_sdk/rpa/input_handler.py
src/omni_bot_sdk/rpa/message_sender.py
src/omni_bot_sdk/rpa/ocr_processor.py
src/omni_bot_sdk/rpa/ui_helper.py
src/omni_bot_sdk/rpa/window_manager.py
src/omni_bot_sdk/rpa/action_handlers/__init__.py
src/omni_bot_sdk/rpa/action_handlers/announcement_handler.py
src/omni_bot_sdk/rpa/action_handlers/base_handler.py
src/omni_bot_sdk/rpa/action_handlers/download_file_handler.py
src/omni_bot_sdk/rpa/action_handlers/download_image_handler.py
src/omni_bot_sdk/rpa/action_handlers/download_video_handler.py
src/omni_bot_sdk/rpa/action_handlers/forward_message_handler.py
src/omni_bot_sdk/rpa/action_handlers/leave_room_handler.py
src/omni_bot_sdk/rpa/action_handlers/pat_handler.py
src/omni_bot_sdk/rpa/action_handlers/remove_room_member_handler.py
src/omni_bot_sdk/rpa/action_handlers/rename_name_in_room_handler.py
src/omni_bot_sdk/rpa/action_handlers/rename_room_name_handler.py
src/omni_bot_sdk/rpa/action_handlers/rename_room_remark_handler.py
src/omni_bot_sdk/rpa/action_handlers/send_file_handler.py
src/omni_bot_sdk/rpa/action_handlers/send_image_handler.py
src/omni_bot_sdk/rpa/action_handlers/send_text_message_handler.py
src/omni_bot_sdk/rpa/action_handlers/switch_conversation_handler.py
src/omni_bot_sdk/rpa/action_handlers/functional/__init__.py
src/omni_bot_sdk/rpa/action_handlers/functional/invite2room_handler.py
src/omni_bot_sdk/rpa/action_handlers/functional/new_friend_handler.py
src/omni_bot_sdk/rpa/action_handlers/functional/send_pyq_handler.py
src/omni_bot_sdk/rpa/action_handlers/mixins/group_operations_mixin.py
src/omni_bot_sdk/rpa/action_handlers/mixins/window_operations_mixin.py
src/omni_bot_sdk/services/__init__.py
src/omni_bot_sdk/services/core/__init__.py
src/omni_bot_sdk/services/core/async_plugin_runner.py
src/omni_bot_sdk/services/core/database_service.cp312-win_amd64.pyd
src/omni_bot_sdk/services/core/message_factory_service.py
src/omni_bot_sdk/services/core/message_service.py
src/omni_bot_sdk/services/core/mqtt_service.py
src/omni_bot_sdk/services/core/processor_service.py
src/omni_bot_sdk/services/core/rpa_service.py
src/omni_bot_sdk/services/core/user_service.py
src/omni_bot_sdk/services/functional/__init__.py
src/omni_bot_sdk/services/functional/dat_decrypt_service.py
src/omni_bot_sdk/services/functional/new_friend_check_service.py
src/omni_bot_sdk/services/functional/weixin_status_service.py
src/omni_bot_sdk/utils/__init__.py
src/omni_bot_sdk/utils/fuck_zxl.cp312-win_amd64.pyd
src/omni_bot_sdk/utils/helpers.py
src/omni_bot_sdk/utils/logging_setup.py
src/omni_bot_sdk/utils/mouse.py
src/omni_bot_sdk/utils/size_config.py
src/omni_bot_sdk/weixin/__init__.py
src/omni_bot_sdk/weixin/message_classes.py
src/omni_bot_sdk/weixin/message_factory.py
src/omni_bot_sdk/weixin/parser/__init__.py
src/omni_bot_sdk/weixin/parser/audio_parser.py
src/omni_bot_sdk/weixin/parser/emoji_parser.py
src/omni_bot_sdk/weixin/parser/file_parser.py
src/omni_bot_sdk/weixin/parser/link_parser.py
src/omni_bot_sdk/weixin/parser/util/__init__.py
src/omni_bot_sdk/weixin/parser/util/common.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/__init__.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/contact_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/emoji_desc_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/file_info_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/msg_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/packed_info_data_img2_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/packed_info_data_img_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/packed_info_data_merged_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/packed_info_data_pb2.py
src/omni_bot_sdk/weixin/parser/util/protocbuf/roomdata_pb2.py
src/omni_bot_sdk/yolo/get_model_path.py
src/omni_bot_sdk/yolo/models/msg_rec.pt