# MCP集成

> <strong>* 带星号的功能为闭源版本功能，用户需自行开发，开源版本不包含相关代码。</strong>

MCP（Multi-Channel Protocol）为机器人提供了丰富的远程控制和自动化能力，支持消息、群管理、朋友圈等多种操作。以下为当前支持的所有 tool 列表：

<table>
  <thead>
    <tr>
      <th style="min-width:180px;">工具名</th>
      <th style="min-width:320px;">参数</th>
      <th style="min-width:320px;">功能说明</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>get_timestamp</code></td>
      <td>无</td>
      <td>获取当前时间的 Unix 时间戳（毫秒）</td>
    </tr>
    <tr>
      <td><code>get_wechat_user_info</code></td>
      <td>无</td>
      <td>获取当前登录的微信用户信息（敏感信息已脱敏）</td>
    </tr>
    <tr>
      <td><code>query_wechat_msg</code></td>
      <td>
        <code>contact_name</code>（对象名称，必填）<br/>
        <code>query</code>（关键字，选填）<br/>
        <code>start_timestamp</code>（起始时间，选填）<br/>
        <code>end_timestamp</code>（结束时间，选填）<br/>
        <code>limit</code>（最大条数，默认500）
      </td>
      <td>查询指定用户或群组的微信消息记录，支持关键字、时间范围、数量限制</td>
    </tr>
    <tr>
      <td><code>send_text_msg</code></td>
      <td>
        <code>recipient_name</code>（对象名称，必填）<br/>
        <code>message</code>（消息内容，必填）<br/>
        <code>at_user_name</code>（@群成员，仅群聊，选填）
      </td>
      <td>向用户或群组发送文本消息，支持@群成员</td>
    </tr>
    <tr>
      <td><code>send_pat_msg</code></td>
      <td>
        <code>user_name</code>（被拍用户昵称，必填）<br/>
        <code>room_name</code>（群聊名称，选填）
      </td>
      <td>发送“拍一拍”消息，支持群聊和单聊</td>
    </tr>
    <tr>
      <td><code>send_file_msg</code></td>
      <td>
        <code>recipient_name</code>（对象名称，必填）<br/>
        <code>file_path</code>（本地文件路径，必填）
      </td>
      <td>向用户或群组发送文件（如图片、视频）</td>
    </tr>
    <tr>
      <td><code>send_pyq*</code></td>
      <td>
        <code>content</code>（文案，选填）<br/>
        <code>images</code>（图片路径列表，选填）
      </td>
      <td>发布一条朋友圈</td>
    </tr>
    <tr>
      <td><code>query_room_member_list</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）
      </td>
      <td>查询指定群聊的成员列表，返回成员的JSON信息</td>
    </tr>
    <tr>
      <td><code>remove_room_member</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>member_name</code>（成员昵称，必填）
      </td>
      <td>从群聊中移除一个成员</td>
    </tr>
    <tr>
      <td><code>invite_room_member</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>user_name</code>（用户昵称，必填）
      </td>
      <td>邀请一个用户加入群聊</td>
    </tr>
    <tr>
      <td><code>public_room_announcement</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>content</code>（公告内容，必填）<br/>
        <code>force_edit</code>（是否强制编辑，选填）
      </td>
      <td>发布或编辑群公告</td>
    </tr>
    <tr>
      <td><code>rename_room_name</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>new_name</code>（新群名，必填）
      </td>
      <td>重命名一个群聊</td>
    </tr>
    <tr>
      <td><code>rename_room_remark</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>new_remark</code>（新备注，必填）
      </td>
      <td>为群聊设置或修改备注</td>
    </tr>
    <tr>
      <td><code>rename_name_in_room</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）<br/>
        <code>new_name_in_room</code>（新昵称，必填）
      </td>
      <td>修改“我”在某个群聊中的昵称</td>
    </tr>
    <tr>
      <td><code>leave_room</code></td>
      <td>
        <code>room_name</code>（群聊名称，必填）
      </td>
      <td>退出一个群聊</td>
    </tr>
  </tbody>
</table> 