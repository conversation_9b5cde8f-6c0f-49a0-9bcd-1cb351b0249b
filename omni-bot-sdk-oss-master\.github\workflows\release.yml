# .github/workflows/release.yml
name: Release Workflow

on:
  push:
    branches:
      - master

jobs:
  # Job 1: Release Please
  release-please:
    runs-on: ubuntu-latest
    outputs:
      release_created: ${{ steps.release.outputs.release_created }}
      tag_name: ${{ steps.release.outputs.tag_name }}
    permissions:
      contents: write
      pull-requests: write
      issues: write
    steps:
      - uses: googleapis/release-please-action@v4
        id: release
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          release-type: python

  # Job 2: Build on Windows
  build:
    needs: release-please
    if: ${{ needs.release-please.outputs.release_created }}
    runs-on: windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install build dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build
      - name: Build package
        run: python -m build
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: python-package
          path: dist/

  # Job 3: Publish from Ubuntu
  publish:
    # 明确依赖 release-please 和 build 两个 job
    needs: [release-please, build]
    if: ${{ needs.release-please.outputs.release_created }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: python-package
          path: dist/

      - name: Publish package to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1

      - name: Upload Release Assets
        uses: softprops/action-gh-release@v2
        with:
          # 直接从 release-please job 获取 tag_name
          tag_name: ${{ needs.release-please.outputs.tag_name }}
          files: dist/*