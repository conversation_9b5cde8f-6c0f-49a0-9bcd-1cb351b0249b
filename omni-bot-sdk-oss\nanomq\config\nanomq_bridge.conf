# #====================================================================
# # MQTT Broker Bridge
# #====================================================================

bridges.mqtt.emqx1 {
	# # Bridge address: host:port .
	# #
	# # Value: String
	# # Example: mqtt-tcp://127.0.0.1:1883
	# #          tls+mqtt-tcp://127.0.0.1:8883
	# #          mqtt-quic://************:14567
	
	server = "mqtt-tcp://127.0.0.1:1883"
	# # Protocol version of the bridge.
	# #
	# # Value: Enum
	# # - 5: mqttv5
	# # - 4: mqttv311
	# # - 3: mqttv31
	proto_ver = 4
	# # The ClientId of a remote bridge.
	# # Default random string.
	# #
	# # Value: String
	# clientid="bridge_client"
	
	# # Ping: interval of a downward bridge.
	# #
	# # Value: Duration
	# # Default: 10 seconds
	keepalive = 60s
	# # The maximum backoff timeout.
	# # Reconnect after no more than backoff_max when bridge connection lost.
	# #
	# # Value: Duration
	# # Default: 60s
	backoff_max = 60s
	# # The Clean start flag of a remote bridge.
	# #
	# # Value: boolean
	# # Default: false
	# #
	# # NOTE: Some IoT platforms require clean_start
	# #       must be set to 'true'
	clean_start = false
	# # The username for a remote bridge.
	# #
	# # Value: String
	username = username
	# # The password for a remote bridge.
	# #
	# # Value: String
	password = passwd
	
	# # Properties for MQTT V5
	conn_properties = {
		# # Maximum Packet Size
		# #
		# # If the Maximum Packet Size is not present, no limit on the packet size is imposed beyond 
		# # the limitations in the protocol as a result of the remaining length encoding and the protocol header sizes.
		# # Value: 1 ~ 4294967295
		maximum_packet_size = 1024
		
		# # Receive Maximum
		# #
		# # The Client uses this value to limit the number of QoS 1 and QoS 2 publications that it is willing to process concurrently. 
		# # There is no mechanism to limit the QoS 0 publications that the Server might try to send.
		# # The value of Receive Maximum applies only to the current Network Connection. 
		# # If the Receive Maximum value is absent then its value defaults to 65,535.
		# #
		# # Value: 1 ~ 65535
		receive_maximum = 65535
		
		# # Topic Alias Maximum
		# #
		# # If the Topic Alias Maximum property is absent, the default value is 0.
		# # This value indicates the highest value that the Client will accept as a Topic Alias sent by the Server. 
		# # The Client uses this value to limit the number of Topic Aliases that it is willing to hold on this Connection.
		# # Default: 0
		# # Value: 0 ~ 65535
		topic_alias_maximum = 0
		
		# # Request Problem Information
		# #
		# # If the Request Problem Information is absent, the value of 1 is used.
		# # The Client uses this value to indicate whether the Reason String or User Properties are sent in the case of failures.
		# # If the value of Request Problem Information is 0, the Server MAY return a Reason String or User Properties on a CONNACK or DISCONNECT packet, 
		# # but MUST NOT send a Reason String or User Properties on any packet other than PUBLISH, CONNACK, or DISCONNECT. 
		# # If the value is 0 and the Client receives a Reason String or User Properties in a packet other than PUBLISH, CONNACK, or DISCONNECT, 
		# # it uses a DISCONNECT packet with Reason Code 0x82 (Protocol Error) as described in section 4.13 Handling errors.
		# # If this value is 1, the Server MAY return a Reason String or User Properties on any packet where it is allowed.
		# # 
		# # Default: 1
		# # Value: 0 | 1
		request_problem_infomation = 1
		
		# # Request Response Information
		# #
		# # If the Request Response Information is absent, the value of 0 is used.
		# # The Client uses this value to request the Server to return Response Information in the CONNACK. 
		# # A value of 0 indicates that the Server MUST NOT return Response Information. 
		# # If the value is 1 the Server MAY return Response Information in the CONNACK packet.
		# #
		# # Default: 0
		# # Value: 0 | 1
		request_response_infomation = 0
		
		# # Session Expiry Interval
		# #
		# # If the Session Expiry Interval is absent the value 0 is used. 
		# # If it is set to 0, or is absent, the Session ends when the Network Connection is closed.
		# # If the Session Expiry Interval is 4294967295 (UINT_MAX), the Session does not expire.
		# #
		# # Value: 0 ~ 4294967295
		session_expiry_interval = 0
		
		# # User Property
		# #
		# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
		# # The same name is allowed to appear more than once.
		# # Value: Map[key(String) - value(String)]
		user_property = {
			key1 = value1
			key2 = value2
		}
	}
	
	will {
		# # Will topic
		topic = "will_topic"
		# # Will QoS
		qos = 1
		# # Will paylad retain flag
		retain = false
		# # Will payload 
		payload = "will_message"
		
		# # Will properties
		properties = {
			# # Payload Format Indicator
			# # 
			# # 0 (0x00) Byte Indicates that the Will Message is unspecified bytes, 
			# # which is equivalent to not sending a Payload Format Indicator.
			# #
			# # 1 (0x01) Byte Indicates that the Will Message is UTF-8 Encoded Character Data.
			# #
			# # Default: 0
			# # Value: 0 | 1
			payload_format_indicator = 0
			
			# # Message Expiry Interval
			# #
			# # If present, the Four Byte value is the lifetime of the Will Message in seconds 
			# # and is sent as the Publication Expiry Interval when the Server publishes the Will Message.
			# # 
			# # If absent, no Message Expiry Interval is sent when the Server publishes the Will Message.
			message_expiry_interval = 0
			
			# # Content Type
			# # The value of the Content Type is defined by the sending and receiving application.
			content_type = ""
			
			# # Response Topic
			# # The presence of a Response Topic identifies the Will Message as a Request.
			response_topic = ""
			
			# # Correlation Data
			# # The Correlation Data is used by the sender of the Request Message to identify which request 
			# # the Response Message is for when it is received.
			correlation_data = ""
			
			# # Will Delay Interval
			# # 
			# # If the Will Delay Interval is absent, the default value is 0 and there is no delay 
			# # before the Will Message is published.
			# #
			# # The Server delays publishing the Client’s Will Message until the Will Delay Interval 
			# # has passed or the Session ends, whichever happens first. 
			# # If a new Network Connection to this Session is made before the Will Delay Interval has passed, the Server MUST NOT send the Will Message
			will_delay_interval = 0
			
			# # User Property
			# #
			# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
			# # The same name is allowed to appear more than once.
			# # Value: Map[key(String) - value(String)]
			user_property = {
				key1 = value1
				key2 = value2
			}
		}
	}
	
	# # Ssl config ##
	# # Ssl config is invalid when working in MQTT over QUIC mode ##
	# ssl {
	# 	# # Ssl key password
	# 	# # String containing the user's password. Only used if the private keyfile
	# 	# # is password-protected.
	# 	# #
	# 	# # Value: String
	# 	# # key_password = "yourpass"
	# 	# # Ssl keyfile
	# 	# # Path of the file containing the client's private key.
	# 	# #
	# 	# # Value: File
	# 	keyfile = "../etc/certs/key.pem"
	# 	# # Ssl cert file
	# 	# # Path of the file containing the client certificate.
	# 	# #
	# 	# # Value: File
	# 	certfile = "../etc/certs/cert.pem"
	# 	# # Ssl ca cert file
	# 	# # Path of the file containing the server's root CA certificate.  
	# 	# # 
	# 	# # This certificate is used to identify the AWS IoT server and is publicly
	# 	# # available.
	# 	# #
	# 	# # Value: File
	# 	cacertfile = "../etc/certs/cacert.pem"
	# }
	
	# # Topics that need to be forward to IoTHUB
	# #
	# # Value: String
	# # Example: topic1/#,topic2/#
	forwards = [
		{
			# # Need to forward to remote broker topics
			# #
			# # Value: String
			remote_topic = "fwd/topic1"
			# # topic reflection with remote_topic
			# #
			# # Value: String
			local_topic = "topic1"
		}
		{
			remote_topic = "fwd/topic2"
			local_topic = "topic2"
		}
	]
	
	# #--------------------------------------------------------------------
	# # The following config params only effective when set QUIC as the
	# # transport layer of bridging connection (mqtt-quic://{host}:{port})!
	# #  
	
	# # Ping: interval of a sending keepalive packet via QUIC transport.
	# #
	# # Value: Duration
	# # Default: 120 seconds
	quic_keepalive = 120s
	# # Idle Timeout: How long a connection can go idle before it is gracefully shut down.
	# #  0 to disable timeout, which may lost disconnect event msg.
	# # Value: Duration
	# # Default: 120 seconds
	quic_idle_timeout = 120s
	# # Disconnect Timeout: How long to wait for an ACK before declaring 
	# # a path dead and disconnecting, This affects stream living time.
	# # Value: Duration
	# # Default: 20 seconds
	quic_discon_timeout = 20s
	# # Handshake Timeout: the Max time NanoMQ waits for establishing QUIC connection
	# # How long a handshake can idle before it is discarded
	# # Value: Duration
	# # Default: 60 seconds
	quic_handshake_timeout = 60s
	# # Send Idle Timeout: Reset congestion control after being idle `SendIdleTimeout`
	# # Value: Duration
	# # Default: 60 seconds
	quic_send_idle_timeout = 2s
	# # Initial RTT: Initial RTT estimate. (ms)
	# # RTT: round trip time
	# # Default: 800ms
	quic_initial_rtt_ms = 800ms
	# # Max Ack Delay: How long to wait after receiving data before sending an ACK.
	# # Value: Duration
	# # Default: 100ms
	quic_max_ack_delay_ms = 100ms
	# # multi-stream mode: enable or disable the multi-stream bridging mode
	# # Warning: This is a feature WIP. Do not enable it!
	# # Value: True/False 
	# # Default: False
	quic_multi_stream = false
	
	# # qos_priority: send QoS 1/2 msg in high prority
	# # QoS 0 messages remain as same
	# # Value: true/false
	# # Default: true
	quic_qos_priority = true
	
	# # 0RTT: enable or diable 0RTT, 0RTT is a feature of QUIC to re-establish
	# # connection quickly.
	# # Value: true/false
	# # Default: true
	quic_0rtt = true
	
	subscription = [
		{
			# # The topic filter of which subscribe to remote broker
			# # This must present to enable the subscription of bridging
			# # 
			# # Value: String
			remote_topic = "cmd/topic3"
			# # This is for Topic reflection, if you want the vanila way:
			# # Leave `local_topic=""` to preserve the original topic in msg
			# # Value: String
			local_topic = "topic3"
			# # Need to subscribe to remote topics QoS.
			# # Please set QoS for each subscription topic
			# # otherwise topic is invalid, NanoMQ won't sub to any topic
			# # Value: Number
			qos = 1
		}
		{
			remote_topic = "cmd/topic4"
			local_topic = "topic4"
			qos = 2
		}
	]
	
	# # Properties of subscribe for MQTT V5
	sub_properties {
		# # Subscription Identifier
		# #
		# # The Subscription Identifier can have the value of 1 to 268,435,455.
		# # It is a Protocol Error if the Subscription Identifier has a value of 0. 
		# # It is a Protocol Error to include the Subscription Identifier more than once.
		# # The Subscription Identifier is associated with any subscription created or modified as the result of this SUBSCRIBE packet. 
		# # If there is a Subscription Identifier, it is stored with the subscription. 
		# # If this property is not specified, then the absence of a Subscription Identifier is stored with the subscription.
		# #
		# # Value: 1 ~ 268,435,455
		identifier = 1
		
		# # User Property
		# #
		# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
		# # The same name is allowed to appear more than once.
		# # 
		# # Value: Map[key(String) - value(String)]
		user_property = {
			key1 = value1
			key2 = value2
		}
	}
	# # Hybrid bridging: enable or disable the hybrid bridging mode
	# # Value: True/False
	# # Default: False
	hybrid_bridging = false

	# # Hybrid servers
	# # When hybrid mode is enabled and the connection to server is
	# # disconnected. Bridge will switch to hybrid_servers in roundrobin.
	# # Value: Array
	# # Default: []
	hybrid_servers = ["mqtt-quic://127.1:14567", "mqtt-tcp://127.1:1883"]

	# # max_parallel_processes
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-infinity
	max_parallel_processes = 2
	
	# # max send queue length
	# # Handle a specified maximum number of message send queue length
	# #
	# # Value: 1-infinity
	max_send_queue_len = 32
	
	# # max receive queue length
	# # Handle a specified maximum number of message receive queue length
	# #
	# # Value: 1-infinity
	max_recv_queue_len = 128
}

bridges.mqtt.cache {
	# # Max message limitation for caching
	# # ( 0 means ineffective )
	# # Value: 1-infinity
	disk_cache_size = 102400
	# # Mounted file path 
	# #
	# # Value: path
	# mounted_file_path="/tmp/"
	
	# # The threshold of flushing messages to flash. 
	# #
	# # Value: 1-infinity
	flush_mem_threshold = 100
	
	# # Resend interval (ms)
	# # The interval for resending the messages after failure recovered. (not related to trigger)
	# # 
	# # Value: 1-infinity 
	resend_interval = 5000
}
