#!/usr/bin/env python3
"""
最小化微信机器人启动脚本
绕过有问题的数据库服务模块，使用基础功能测试
"""

import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_basic_imports():
    """测试基础模块导入"""
    print("🔍 测试基础模块导入...")
    
    try:
        import omni_bot_sdk
        print("✅ omni_bot_sdk 基础模块导入成功")
    except Exception as e:
        print(f"❌ omni_bot_sdk 导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.common.config import Config
        print("✅ 配置模块导入成功")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.plugins.plugin_manager import PluginManager
        print("✅ 插件管理器导入成功")
    except Exception as e:
        print(f"❌ 插件管理器导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.mcp.server import MCPServer
        print("✅ MCP服务器导入成功")
    except Exception as e:
        print(f"❌ MCP服务器导入失败: {e}")
        return False
    
    return True

def test_config_loading():
    """测试配置文件加载"""
    print("\n🔍 测试配置文件加载...")
    
    try:
        from omni_bot_sdk.common.config import Config
        config = Config("config.yaml")
        print("✅ 配置文件加载成功")
        print(f"   - MQTT主机: {config.mqtt.host}")
        print(f"   - MCP端口: {config.mcp.port}")
        return True
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def test_mcp_server():
    """测试MCP服务器启动"""
    print("\n🔍 测试MCP服务器...")
    
    try:
        from omni_bot_sdk.mcp.server import MCPServer
        from omni_bot_sdk.common.config import Config
        
        config = Config("config.yaml")
        server = MCPServer(config)
        print("✅ MCP服务器创建成功")
        print(f"   - 服务器地址: {config.mcp.host}:{config.mcp.port}")
        return True
    except Exception as e:
        print(f"❌ MCP服务器创建失败: {e}")
        return False

def main():
    print("🤖 微信机器人最小化测试")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查配置文件
    config_path = project_root / "config.yaml"
    if not config_path.exists():
        print("❌ 配置文件 config.yaml 不存在")
        print("请先创建配置文件")
        return
    
    print("✅ 配置文件存在")
    
    # 测试基础导入
    if not test_basic_imports():
        print("\n❌ 基础模块导入失败")
        return
    
    # 测试配置加载
    if not test_config_loading():
        print("\n❌ 配置加载失败")
        return
    
    # 测试MCP服务器
    if not test_mcp_server():
        print("\n❌ MCP服务器测试失败")
        return
    
    print("\n🎉 所有基础功能测试通过！")
    print("\n📋 下一步操作：")
    print("1. 获取微信数据库密钥并填入config.yaml")
    print("2. 启动MQTT服务")
    print("3. 确保微信客户端已登录")
    print("4. 尝试启动完整的机器人服务")
    
    # 询问是否启动MCP服务器
    try:
        choice = input("\n是否启动MCP服务器进行测试？(y/n): ").lower().strip()
        if choice == 'y':
            print("\n🚀 启动MCP服务器...")
            from omni_bot_sdk.mcp.server import MCPServer
            from omni_bot_sdk.common.config import Config
            
            config = Config("config.yaml")
            server = MCPServer(config)
            
            print(f"MCP服务器已启动在 http://{config.mcp.host}:{config.mcp.port}")
            print("按 Ctrl+C 停止服务器")
            
            server.start()
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
