# NanoMQ Configuration 0.23.1

# #============================================================
# # NanoMQ Broker
# #============================================================

system {
	# # num_taskq_thread
	# # Use a specified number of taskq threads 
	# #
	# # Value: 1-255, Obtain automatically if 0
	num_taskq_thread = 0
	
	# # max_taskq_thread
	# # Use a specified maximunm number of taskq threads
	# #
	# # Value: 1-255, Obtain automatically if 0
	max_taskq_thread = 0
	
	# # parallel
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-255, Obtain automatically if 0
	parallel = 0
	
	# # hook_ipc_url
	# # Specify hook ipc url
	# #
	## Value: ipc://path
	hook_ipc_url = "ipc:///tmp/nanomq_hook.ipc"

	# # cmd_ipc_url
	# # Specify cmd ipc url
	# #
	# # Value: ipc://path
	cmd_ipc_url = "ipc:///tmp/nanomq_cmd.ipc"

}

mqtt {
	# # max_packet_size
	# # Defines the default max size of a packet that NanoMQ could accept and send
	# #
	# # Hot updatable
	# # Value: 1 Byte-260 MB
	max_packet_size = 1KB
	
	# # max_mqueue_len
	# # The queue length in-flight window
	# # This is essential for performance and memory consumption
	# #
	# # Hot updatable
	# # Value: 1-infinity
	max_mqueue_len = 2048
	
	# # Unsupported now
	max_inflight_window = 2048
	max_awaiting_rel = 10s
	await_rel_timeout = 10s
	
	# # retry_interval (s)
	# # The retry interval is nano qos duration which also controls timer 
	# # interval of each pipe
	# #
	# # Hot updatable
	# # Value: 1-infinity
	retry_interval = 10s
	
	# # The backoff for MQTT keepalive timeout.
	# # broker will discolse client when there is no activity for
	# # 'Keepalive * backoff * timeout.
	# #
	# # Hot updatable
	# # Value: Float > 0.5
	keepalive_multiplier = 1.25
	
	# # property_size
	# # The max size for a MQTT user property
	# #
	# # Hot updatable
	# # Value: 1-infinity
	property_size = 32
	
}

listeners.tcp {
	# # bind
	# # Connect with the host and port
	# #
	# # Value: host:port
	bind = "0.0.0.0:1883"
}

listeners.tcp.default_1 {
    bind = "0.0.0.0:1884"
}

listeners.tcp.default_2 {
    bind = "0.0.0.0:1885"
}

# #============================================================
# # TLS/SSL
# #============================================================

listeners.ssl {
	# # tls url
	# #
	# # Value: "host:port"
	bind = "0.0.0.0:8883"
	
	# # tls key password
	# # String containing the user's password. Only used if the private keyfile
	# # is password-protected.
	# #
	# # Value: String
	# key_password="yourpass"
	
	# # tls keyfile
	# # Path to the file containing the user's private PEM-encoded key.
	# #
	# # Value: File
	keyfile = "/etc/certs/key.pem"
	
	# # tls cert file
	# # Path to a file containing the user certificate.
	# #
	# # Value: File
	certfile = "/etc/certs/cert.pem"
	
	# # tls ca cert file
	# # Path to the file containing PEM-encoded CA certificates. The CA certificates
	# # are used during server authentication and when building the client certificate chain.
	# #
	# # Value: File
	cacertfile = "/etc/certs/cacert.pem"
	
	# # A server only does x509-path validation in mode verify_peer,
	# # as it then sends a certificate request to the client (this
	# # message is not sent if the verify option is verify_none).
	# # You can then also want to specify option fail_if_no_peer_cert.
	# #
	# # Value: true: verify_peer | false: verify_none
	verify_peer = false
	
	# # Used together with {verify, verify_peer} by an SSL server. If set to true,
	# # the server fails if the client does not have a certificate to send, that is,
	# # sends an empty certificate.
	# #
	# # Value: true | false
	fail_if_no_peer_cert = false
}

listeners.ssl.default_1 {
	bind = "0.0.0.0:8881"
	keyfile = "/etc/certs/key.pem"
	certfile = "/etc/certs/cert.pem"
	cacertfile = "/etc/certs/cacert.pem"
	verify_peer = false
	fail_if_no_peer_cert = false
}

listeners.ssl.default_2 {
	bind = "0.0.0.0:8884"
	keyfile = "/etc/certs/key.pem"
	certfile = "/etc/certs/cert.pem"
	cacertfile = "/etc/certs/cacert.pem"
	verify_peer = false
	fail_if_no_peer_cert = false
}

# #============================================================
# # WebSocket
# #============================================================
listeners.ws {
	# # websocket url
	# #
	# # Value: "host:port/path"
	bind = "0.0.0.0:8083/mqtt"
}

listeners.wss {
	# # websocket tls url
	# #
	# # Value: "host:port/path"
	bind = "0.0.0.0:8086/mqtt"
}

# # -------------------- SQLite Config -------------------- ##
sqlite {
	# # Max message limitation for caching
	# # ( 0 means ineffective )
	# # Value: 1-infinity
	disk_cache_size = 102400
	# # Mounted file path 
	# #
	# # Value: path
	# mounted_file_path="/tmp/"
	
	# # The threshold of flushing messages to flash. 
	# #
	# # Hot updatable
	# # Value: 1-infinity
	flush_mem_threshold = 100
	
	# # Resend interval (ms)
	# # The interval for resending the messages after failure recovered. (not related to trigger)
	# # 
	# # Value: 1-infinity 
	resend_interval = 5000
}

# #============================================================
# # Http server
# #============================================================
http_server {
	# # http server port
	# #
	# # Value: 0 - 65535
	port = 8081
	# # http server addr
	# #
	# # Value: ip address only, *********** or 0.0.0.0 or localhost
	ip_addr = 0.0.0.0
	# # limit connector
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-infinity
	limit_conn = 32
	# # http server username
	# #
	# # Value: String
	username = admin
	# # http server password
	# #
	# # Value: String
	password = public
	# # http server auth type
	# # If set auth_type=jwt, make sure you have built JWT dependency with `-DENABLE_JWT=ON` first.
	# #
	# # Value: String basic | jwt
	auth_type = basic
	jwt {
		# # http server jwt public key file
		# # Used together with 'http_server.auth_type=jwt',
		# # Path to the file containing the user's private key.
		# #
		# # Value: File
		public.keyfile = "/etc/certs/jwt/jwtRS256.key.pub"
	}
}

# # ------------------ Logging Config ------------------ ##
log {
	# # Where to emit the logs.
	# #
	# # - file: write logs to file
	# # - console: write logs to standard I/O
	# # - syslog: write logs to syslog
	# # Value: file | console | syslog
	# # Example: file,console,syslog
	to = [file, console]
	# # The log severity level.
	# #
	# # Value: trace | debug | info | warn | error | fatal
	# #
	# # Note: Only the messages with severity level higher than or equal to
	# #       this level will be logged.
	# #
	# # Default: warn
	level = warn
	# # The dir for log files.
	# #
	# # Value: Folder
	dir = "/tmp"
	# # The log filename for logs of level specified in "log.level".
	# #
	# # Value: String
	# # Default: nanomq.log
	file = "nanomq.log"
	rotation {
		# # Maximum size of each log file.
		# #
		# # Value: Number
		# # Default: 10M
		# # Supported Unit: KB | MB | GB
		size = 10MB
		# # Maximum rotation count of log files.
		# #
		# # Value: Number
		# # Default: 5
		count = 5
	}
}

# #============================================================
# # WebHook
# #============================================================

# webhook {
# ## Webhook URL
# ##
# ## Value: String
# url="http://127.0.0.1:80"
# ## HTTP Headers
# ##
# ## Example:
# ## 1. web.hook.headers.content-type="application/json"
# ## 2. web.hook.headers.accept="*"
# ##
# ## Value: String
# headers.content-type="application/json"
# ## The encoding format of the payload field in the HTTP body
# ## The payload field only appears in the on_message_publish and on_message_delivered actions
# ##
# ## Value: plain | base64 | base62
# body.encoding="plain"
# ## Connection process pool size
# ##
# ## Value: Number
# pool_size=32
# 
# # Unsupport now
# # tls {
# # 	keyfile="/etc/certs/key.pem"
# # 	certfile="/etc/certs/cert.pem"
# # 	cacertfile="/etc/certs/cacert.pem"
# # }
# 
# }

webhook {
	url = "http://127.0.0.1:80"
	headers.content-type = "application/json"
	body.encoding = plain
	pool_size = 32
	
	events = [
		{ 
			# # Webhook event.
			# #
			# # Value: String
			# # Supported event list:
			# # event: on_client_connect
			# # event: on_client_connack
			# # event: on_client_connected
			# # event: on_client_disconnected
			# # event: on_client_subscribe
			# # event: on_client_unsubscribe
			# # event: on_session_subscribed
			# # event: on_session_unsubscribed
			# # event: on_session_terminated
			# # event: on_message_publish
			# # event: on_message_delivered
			# # event: on_message_acked
			event = "on_message_publish"

			# # Webhook topic.
			# #
			# # Value: String
			# # Support on message publish
			topic = "a/b/c"
		}
		{
			event = "on_client_connack"
		}
	]
}

auth {
	# # anonymous
	# # allow anonymous login
	# #
	# # Hot updatable
	# # Value: true | false
	allow_anonymous = true
	
	# # Allow or deny if no ACL rules matched.
	# #
	# # Value: allow | deny
	no_match = allow
	
	# # The action when acl check reject current operation
	# #
	# # Value: ignore | disconnect
	# # Default: ignore
	deny_action = ignore
	
	cache = {
		# # The maximum count of ACL entries can be cached for a client.
		# #
		# # Value: Integer greater than 0
		# # Default: 32
		max_size = 32
		
		# # The time after which an ACL cache entry will be deleted
		# #
		# # Value: Duration
		# # Default: 1 minute
		ttl = 1m
	}
	
	# # This is password conf file.
	# #
	# # Value: path string
	# # Default: "/etc/pwd.conf"
	password = {include "/etc/nanomq_pwd.conf"}
	
	# # This is acl conf file.
	# #
	# # Value: path string
	# # Default: "/etc/acl.conf"
	acl = {include "/etc/nanomq_acl.conf"}
	
	http_auth = {
		auth_req {
			# # HTTP URL API path for Auth Request
			# #
			# # Value: URL
			# #
			# # Examples: http://127.0.0.1:80/mqtt/auth, https://[::1]:80/mqtt/auth
			url = "http://127.0.0.1:80/mqtt/auth"
			# # HTTP Request Method for Auth Request
			# #
			# # Value: post | get
			method = post
			# # HTTP Request Headers for Auth Request, Content-Type header is configured by default.
			# # The possible values of the Content-Type header: application/x-www-form-urlencoded, application/json
			# #
			# # Examples: auth.http.auth_req.headers.accept = */*
			headers.content-type = "application/x-www-form-urlencoded"
			# # Parameters used to construct the request body or query string parameters
			# # When the request method is GET, these parameters will be converted into query string parameters
			# # When the request method is POST, the final format is determined by content-type
			# #
			# # Available Variables:
			# #  - %u: username
			# #  - %c: clientid
			# #  - %a: ipaddress
			# #  - %r: protocol
			# #  - %P: password
			# #  - %p: sockport of server accepted
			# #  - %C: common name of client TLS cert
			# #  - %d: subject of client TLS cert
			# #
			# # Value: <K1>=<V1>,<K2>=<V2>,...
			params = {clientid = "%c", username = "%u", password = "%p"}
			# Unsupport now
			# tls {
			# keyfile="/etc/certs/key.pem"
			# certfile="/etc/certs/cert.pem"
			# cacertfile="/etc/certs/cacert.pem"
			# }
		}
		
		super_req {
			url = "http://127.0.0.1:80/mqtt/superuser"
			method = "post"
			headers.content-type = "application/x-www-form-urlencoded"
			params = {clientid = "%c", username = "%u", password = "%p"}
			# Unsupport now
			# tls {
			# keyfile="/etc/certs/key.pem"
			# certfile="/etc/certs/cert.pem"
			# cacertfile="/etc/certs/cacert.pem"
			# }
			
		}
		# # HTTP ACL request is unsupported.
		acl_req {
			url = "http://127.0.0.1:8991/mqtt/acl"
			method = "post"
			headers.content-type = "application/x-www-form-urlencoded"
			params = {clientid = "%c", username = "%u", access = "%A", ipaddr = "%a", topic = "%t", mountpoint = "%m"}
			# Unsupport now
			# tls {
			# keyfile="/etc/certs/key.pem"
			# certfile="/etc/certs/cert.pem"
			# cacertfile="/etc/certs/cacert.pem"
			# }
			
		}
		
		# # Time-out time for the request.
		# #
		# # Value: Duration
		# # -h: hour, e.g. '2h' for 2 hours
		# # -m: minute, e.g. '5m' for 5 minutes
		# # -s: second, e.g. '30s' for 30 seconds
		# #
		# # Default: 5s
		timeout = 5s
		# # Connection time-out time, used during the initial request,
		# # when the client is connecting to the server.
		# #
		# # Value: Duration
		# # -h: hour, e.g. '2h' for 2 hours
		# # -m: minute, e.g. '5m' for 5 minutes
		# # -s: second, e.g. '30s' for 30 seconds
		# #
		# # Default: 5s
		connect_timeout = 5s
		# # Connection process pool size
		# #
		# # Value: Number
		pool_size = 32
	}
}

# #====================================================================
# # Preset MQTT sessions 
# #====================================================================
# # With preset sessions, You can publish messages to a void client,
# # that is not connected yet. QoS messages will be cached just like session keeping
# # However, the new coming client still need to subscribe to the target topics by itself
preset.session.1 {
	clientid = "example"
	topic = [
		{
			qos = 2
			remote_topic = "msg1/#"
		},
		{
			qos = 1
			remote_topic = "msg2/#"
		}
	]
}

# #====================================================================
# # MQTT Broker Bridge
# #====================================================================
# # Here, you can use the include syntax of HOCON to put the
# # configuration file of the bridge into nanomq_bridge.conf.
# # include "path/to/nanomq_bridge.conf" 
bridges.mqtt.emqx1 {
	# # Bridge address: host:port .
	# #
	# # Value: String
	# # Example: mqtt-tcp://127.0.0.1:1883
	# #          tls+mqtt-tcp://127.0.0.1:8883
	# #          mqtt-quic://************:14567
	
	server = "mqtt-tcp://127.0.0.1:1883"
	# # Protocol version of the bridge.
	# #
	# # Value: Enum
	# # - 5: mqttv5
	# # - 4: mqttv311
	# # - 3: mqttv31
	proto_ver = 5

	# # The ClientId of a remote bridge.
	# # Default random string.
	# #
	# # Value: String
	clientid="bridge_client"
	
	# # Ping: interval of a downward bridge.
	# #
	# # Value: Duration
	# # Default: 10 seconds
	keepalive = 60s
	# # The maximum backoff timeout.
	# # Reconnect after no more than backoff_max when bridge connection lost.
	# #
	# # Value: Duration
	# # Default: 60s
	backoff_max = 60s
	# # The Clean start flag of a remote bridge.
	# #
	# # Value: boolean
	# # Default: false
	# #
	# # NOTE: Some IoT platforms require clean_start
	# #       must be set to 'true'
	clean_start = false
	# # The transparent proxy flag of the bridging client
	# #
	# # Value: boolean
	# # Default: false
	# #
	# # NOTE: This option gonna proxy Sub/UnSub action
	# # of all local client to this bridging connection as well 
	transparent = false
	# # The username for a remote bridge.
	# #
	# # Value: String
	username = username
	# # The password for a remote bridge.
	# #
	# # Value: String
	password = passwd
	
	# # Properties for MQTT V5
	conn_properties = {
		# # Maximum Packet Size
		# #
		# # If the Maximum Packet Size is not present, no limit on the packet size is imposed beyond 
		# # the limitations in the protocol as a result of the remaining length encoding and the protocol header sizes.
		# # Value: 1 ~ 4294967295
		maximum_packet_size = 1024
		
		# # Receive Maximum
		# #
		# # The Client uses this value to limit the number of QoS 1 and QoS 2 publications that it is willing to process concurrently. 
		# # There is no mechanism to limit the QoS 0 publications that the Server might try to send.
		# # The value of Receive Maximum applies only to the current Network Connection. 
		# # If the Receive Maximum value is absent then its value defaults to 65,535.
		# #
		# # Value: 1 ~ 65535
		receive_maximum = 65535
		
		# # Topic Alias Maximum
		# #
		# # If the Topic Alias Maximum property is absent, the default value is 0.
		# # This value indicates the highest value that the Client will accept as a Topic Alias sent by the Server. 
		# # The Client uses this value to limit the number of Topic Aliases that it is willing to hold on this Connection.
		# # Default: 0
		# # Value: 0 ~ 65535
		topic_alias_maximum = 0
		
		# # Request Problem Information
		# #
		# # If the Request Problem Information is absent, the value of 1 is used.
		# # The Client uses this value to indicate whether the Reason String or User Properties are sent in the case of failures.
		# # If the value of Request Problem Information is 0, the Server MAY return a Reason String or User Properties on a CONNACK or DISCONNECT packet, 
		# # but MUST NOT send a Reason String or User Properties on any packet other than PUBLISH, CONNACK, or DISCONNECT. 
		# # If the value is 0 and the Client receives a Reason String or User Properties in a packet other than PUBLISH, CONNACK, or DISCONNECT, 
		# # it uses a DISCONNECT packet with Reason Code 0x82 (Protocol Error) as described in section 4.13 Handling errors.
		# # If this value is 1, the Server MAY return a Reason String or User Properties on any packet where it is allowed.
		# # 
		# # Default: 1
		# # Value: 0 | 1
		request_problem_infomation = 1
		
		# # Request Response Information
		# #
		# # If the Request Response Information is absent, the value of 0 is used.
		# # The Client uses this value to request the Server to return Response Information in the CONNACK. 
		# # A value of 0 indicates that the Server MUST NOT return Response Information. 
		# # If the value is 1 the Server MAY return Response Information in the CONNACK packet.
		# #
		# # Default: 0
		# # Value: 0 | 1
		request_response_infomation = 0
		
		# # Session Expiry Interval
		# #
		# # If the Session Expiry Interval is absent the value 0 is used. 
		# # If it is set to 0, or is absent, the Session ends when the Network Connection is closed.
		# # If the Session Expiry Interval is 4294967295 (UINT_MAX), the Session does not expire.
		# #
		# # Value: 0 ~ 4294967295
		session_expiry_interval = 0
		
		# # User Property
		# #
		# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
		# # The same name is allowed to appear more than once.
		# # Value: Map[key(String) - value(String)]
		user_property = {
			key1 = value1
			key2 = value2
		}
	}
	
	will {
		# # Will topic
		topic = "will_topic"
		# # Will QoS
		qos = 1
		# # Will paylad retain flag
		retain = false
		# # Will payload 
		payload = "will_message"
		
		# # Will properties
		properties = {
			# # Payload Format Indicator
			# # 
			# # 0 (0x00) Byte Indicates that the Will Message is unspecified bytes, 
			# # which is equivalent to not sending a Payload Format Indicator.
			# #
			# # 1 (0x01) Byte Indicates that the Will Message is UTF-8 Encoded Character Data.
			# #
			# # Default: 0
			# # Value: 0 | 1
			payload_format_indicator = 0
			
			# # Message Expiry Interval
			# #
			# # If present, the Four Byte value is the lifetime of the Will Message in seconds 
			# # and is sent as the Publication Expiry Interval when the Server publishes the Will Message.
			# # 
			# # If absent, no Message Expiry Interval is sent when the Server publishes the Will Message.
			message_expiry_interval = 0
			
			# # Content Type
			# # The value of the Content Type is defined by the sending and receiving application.
			content_type = ""
			
			# # Response Topic
			# # The presence of a Response Topic identifies the Will Message as a Request.
			response_topic = ""
			
			# # Correlation Data
			# # The Correlation Data is used by the sender of the Request Message to identify which request 
			# # the Response Message is for when it is received.
			correlation_data = ""
			
			# # Will Delay Interval
			# # 
			# # If the Will Delay Interval is absent, the default value is 0 and there is no delay 
			# # before the Will Message is published.
			# #
			# # The Server delays publishing the Client’s Will Message until the Will Delay Interval 
			# # has passed or the Session ends, whichever happens first. 
			# # If a new Network Connection to this Session is made before the Will Delay Interval has passed, the Server MUST NOT send the Will Message
			will_delay_interval = 0
			
			# # User Property
			# #
			# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
			# # The same name is allowed to appear more than once.
			# # Value: Map[key(String) - value(String)]
			user_property = {
				key1 = value1
				key2 = value2
			}
		}
	}
	
    # # Ssl config ##
    ## ssl {
    ##    # # Ssl key password
    ##    # # String containing the user's password. Only used if the private keyfile
    ##    # # is password-protected.
    ##    # #
    ##    # # Value: String
    ##    key_password = "yourpass"
    ##    # # Ssl keyfile
    ##    # # Path of the file containing the client's private key.
    ##    # #
    ##    # # Value: File
    ##    keyfile = "/etc/certs/key.pem"
    ##    # # Ssl cert file
    ##    # # Path of the file containing the client certificate.
    ##    # #
    ##    # # Value: File
    ##    certfile = "/etc/certs/cert.pem"
    ##    # # Ssl ca cert file
    ##    # # Path of the file containing the server's root CA certificate.
    ##    # #
    ##    # # Value: File
    ##    cacertfile = "/etc/certs/cacert.pem"
    ##}
	
	# # Topics that need to be forward to IoTHUB
	# #
	# # Value: String
	forwards = [
		{
			# # This is for Topic reflection, if you want the vanila way:
			# # Leave `remote_topic=""` to preserve the original topic in msg
			# #
			# # Value: String
			remote_topic = "fwd/topic1"
			# # The topic filter of which to forward to remote broker
			# #
			# # Local topic means the original topic of locale publish msg
			# # msgs from local_topic will be reflected to remote_topic
			# # This must present to enable the forwarding of bridging 
			# # Value: String
			local_topic = "topic1"
			# # Retain is used to override the ratain flag in the msg is about
			# # to forward to remote. (0|1) stand for override the retain flag with (0|1).
			# # 2 or not set this value will keep retain flag as it is.
			# # Value: Number (0|1|2)
			# # Default: 2
			# retain = 2

			# # Prefix string adds to the remote topic.(or original topic if you leave remote_topic as "")
			# # Value: String
			# # Default: NULL
			# prefix = ""

			# # Suffix string adds to the remote topic.(or original topic if you leave remote_topic as "")
			# # Value: String
			# # Default: NULL
			# suffix = ""
		}
		{
			remote_topic = "fwd/topic2"
			local_topic = "topic2"
		}
	]

	# #--------------------------------------------------------------------
	# # The following config params only effective when set QUIC as the
	# # transport layer of bridging connection (mqtt-quic://{host}:{port})!
	# #  
	
	# # Ping: interval of a sending keepalive packet via QUIC transport.
	# #
	# # Value: Duration
	# # Default: 120 seconds
	quic_keepalive = 120s
	# # Idle Timeout: How long a connection can go idle before it is gracefully shut down.
	# #  0 to disable timeout, which may lost disconnect event msg.
	# # Value: Duration
	# # Default: 120 seconds
	quic_idle_timeout = 120s
	# # Disconnect Timeout: How long to wait for an ACK before declaring 
	# # a path dead and disconnecting, This affects stream living time.
	# # Value: Duration
	# # Default: 20 seconds
	quic_discon_timeout = 20s
	# # Handshake Timeout: the Max time NanoMQ waits for establishing QUIC connection
	# # How long a handshake can idle before it is discarded
	# # Value: Duration
	# # Default: 60 seconds
	quic_handshake_timeout = 60s
	# # Send Idle Timeout: Reset congestion control after being idle `SendIdleTimeout`
	# # Value: Duration
	# # Default: 60 seconds
	quic_send_idle_timeout = 2s
	# # Initial RTT: Initial RTT estimate. (ms)
	# # RTT: round trip time
	# # Default: 800ms
	quic_initial_rtt_ms = 800ms
	# # Max Ack Delay: How long to wait after receiving data before sending an ACK.
	# # Value: Duration
	# # Default: 100ms
	quic_max_ack_delay_ms = 100ms
	# # multi-stream mode: enable or disable the multi-stream bridging mode
	# # Warning: This is a feature WIP. Do not enable it!
	# # Value: True/False 
	# # Default: False
	quic_multi_stream = false
	
	# # qos_priority: send QoS 1/2 msg in high prority
	# # QoS 0 messages remain as same
	# # Value: true/false
	# # Default: true
	quic_qos_priority = true
	
	# # 0RTT: enable or diable 0RTT, 0RTT is a feature of QUIC to re-establish
	# # connection quickly.
	# # Value: true/false
	# # Default: true
	quic_0rtt = true
	
	subscription = [
		{
			# # The topic filter of which subscribe to remote broker
			# # This must present to enable the subscription of bridging
			# # 
			# # Value: String
			remote_topic = "cmd/topic3"

			# # This is for Topic reflection, if you want the vanila way:
			# # Leave `local_topic=""` to preserve the original topic in msg
			# # Value: String
			local_topic = "topic3"

			# # Need to subscribe to remote topics QoS.
			# # Please set QoS for each subscription topic
			# # otherwise topic is invalid, NanoMQ won't sub to any topic
			# # Value: Number
			qos = 1

			# # Retain is used to override the ratain flag in the msg is about
			# # to forward to local. (0|1) stand for override the retain flag with (0|1).
			# # 2 or not set this value will keep retain flag as it is.
			# # Value: Number (0|1|2)
			# # Default: 2
			# # retain = 2

			# # Retain As Published is used to tell broker what to do with retain flag.
			# # If 1, message forwarded using this subscription have the RETAIN flag they were published with.
			# # If 0, message forwarded using this subscription have the RETAIN flag set to 0.
			# # Value: Number (0|1)
			retain_as_published = 1

			# # Retain Handling is used to tell broker whether retain message should be sent.
			# # If Retain Handing is set to 0, send all retain messages.
			# # If Retain Handing is set to 1, send retain messages only if this subscription is not already exist. Otherwise do not send.
			# # If Retain Handing is set to 2, do not send any retain message.
			# # Value: Number (0|1|2)
			retain_handling = 2

			# # Prefix string adds to the local topic.(or original topic if you leave local_topic as "")
			# # Value: String
			# # Default: NULL
			# prefix = ""

			# # Suffix string adds to the local topic.(or original topic if you leave local_topic as "")
			# # Value: String
			# # Default: NULL
			# suffix = ""
		}
		{
			remote_topic = "cmd/topic4"
			local_topic = "topic4"
			qos = 2
		}
	]
	
	# # Properties of subscribe for MQTT V5
	sub_properties {
		# # Subscription Identifier
		# #
		# # The Subscription Identifier can have the value of 1 to 268,435,455.
		# # It is a Protocol Error if the Subscription Identifier has a value of 0. 
		# # It is a Protocol Error to include the Subscription Identifier more than once.
		# # The Subscription Identifier is associated with any subscription created or modified as the result of this SUBSCRIBE packet. 
		# # If there is a Subscription Identifier, it is stored with the subscription. 
		# # If this property is not specified, then the absence of a Subscription Identifier is stored with the subscription.
		# #
		# # Value: 1 ~ 268,435,455
		identifier = 1
		
		# # User Property
		# #
		# # The User Property is allowed to appear multiple times to represent multiple name, value pairs. 
		# # The same name is allowed to appear more than once.
		# # 
		# # Value: Map[key(String) - value(String)]
		user_property = {
			key1 = value1
			key2 = value2
		}
	}

	# # This part only takes effect if using TCP/TLS bridging, not QUIC.
	tcp {
	# # allows fine tuning of TCP options.
	# Interface binding: only send packet to specific interface
	# 	bind_interface = wlan0

	# # nodelay: equals to `nodelay` from POSIX standard
	#	     but also serves as the switch of a fail interface binding action
	#	     `true` keeps retrying. `false` ignore fales, skip this time.
		nodelay = false
	# #  keepidle: equals to `keepidle` from POSIX standard.
	# 		It is the heartbeat in TCP layer.
	# 	keepidle = 60000
	}
	# # Hybrid bridging: enable or disable the hybrid bridging mode
	# # Value: True/False
	# # Default: False
	hybrid_bridging = false

	# # Hybrid servers
	# # When hybrid mode is enabled and the connection to server is
	# # disconnected. Bridge will switch to hybrid_servers in roundrobin.
	# # Value: Array
	# # Default: []
	hybrid_servers = ["mqtt-quic://127.1:14567", "mqtt-tcp://127.1:1883"]

	# # max_parallel_processes
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-infinity
	max_parallel_processes = 2
	
	# # max send queue length
	# # Handle a specified maximum number of message send queue length
	# #
	# # Value: 1-infinity
	max_send_queue_len = 32
	
	# # max receive queue length
	# # Handle a specified maximum number of message receive queue length
	# #
	# # Value: 1-infinity
	max_recv_queue_len = 128

	# # Resend interval (ms)
	# # The interval for resending the messages after failure recovered. (not related to trigger)
	# # move from cache to bridge since 0.22.8
	# # Value: 1-infinity (uint64)
	resend_interval = 5000

	# # Resend wait time (ms)
	# # The waiting time for resending the messages after it is publiushed.
	# # Tips: set it longer than keepalive if you dont want too much duplicated msg
	# # Value: 1-infinity (uint64)
	resend_wait = 3000

	# # max Ack wait time for each QoS msg (ms)
	# # The waiting time for Acknowledgment of every QoS msg. Does not affect normal sending.
	# # Tips: QoS msg requries a delivered ack, which occupies an AIO.
	# # 	  set a max timeout time to cancel the ack action.
	# #       once it is canceled, there is no more retrying of this msg.
	# # Value: 1-infinity (uint64)
	cancel_timeout = 8000

        # # Enable or disable QoS 0 message chech on SQLite for retrying  (bool)
        # # Proposed by ftapajos, to reserve disk space for QoS 1/2 msg
        # # This option is only valid when SQLite is enabled
        # # Value: true or false
        retry_qos_0 = false
}

# # The configuration of this cache is shared by all MQTT bridges.
bridges.mqtt.cache {
	# # Max message limitation for caching
	# # ( 0 means ineffective )
	# # Value: 1-infinity
	disk_cache_size = 102400
	# # Mounted file path 
	# #
	# # Value: path
	# mounted_file_path="/tmp/"
	
	# # The threshold of flushing messages to flash. 
	# #
	# # Value: 1-infinity
	flush_mem_threshold = 100
}



# #====================================================================
# # AWS IoT Core Bridge
# #====================================================================
bridges.aws.c1 {
	# # Aws address: host:port .
	# #
	# # Value: String
	server = "127.0.0.1:8883"
	# # Protocol version of the bridge.
	# #
	# # Value: Enum
	# # - 5: mqttv5
	# # - 4: mqttv311
	proto_ver = 4
	# # The ClientId of a remote bridge.
	# # Default random string.
	# #
	# # Value: String
	clientid = "aws_bridge_client"
	# # Ping interval of a down bridge.
	# #
	# # Value: Duration
	# # Default: 10 seconds
	keepalive = 60s
	# # The Clean start flag of a remote bridge.
	# #
	# # Value: boolean
	# # Default: true
	# #
	# # NOTE: Some IoT platforms require clean_start
	# #       must be set to 'true'
	clean_start = true
	# # The username for a remote bridge.
	# #
	# # Value: String
	# username = "username"
	# # The password for a remote bridge.
	# #
	# # Value: String
	# password = "passwd"
	
	# ssl {
	# # # Ssl key password
	# # # String containing the user's password. Only used if the private keyfile
	# # # is password-protected.
	# # #
	# # # Value: String
	# key_password = "yourpass"
	# # # Ssl keyfile
	# # # Path of the file containing the client's private key.
	# # #
	# # # Value: File
	# keyfile = "/etc/certs/key.pem"
	# # # Ssl cert file
	# # # Path of the file containing the client certificate.
	# # #
	# # # Value: File
	# certfile = "/etc/certs/cert.pem"
	# # # Ssl ca cert file
	# # # Path of the file containing the server's root CA certificate.  
	# # # 
	# # # This certificate is used to identify the AWS IoT server and is publicly
	# # # available.
	# # #
	# # # Value: File
	# cacertfile = "/etc/certs/cacert.pem"
	# }
	
	# # Topics that need to be forward to IoTHUB
	# #
	# # Value: String
	forwards = [
		{
			# # Need to forward to remote broker topics
			# #
			# # Value: String
			remote_topic = "fwd/topic1"
			# # topic reflection with remote_topic
			# #
			# # Value: String
			local_topic = "topic1"
		}
		{
			remote_topic = "fwd/topic2"
			local_topic = "topic2"
		}
	]

	subscription = [
		{
			# # Need to subscribe to remote broker topics
			# #
			# # Value: String
			remote_topic = "cmd/topic1"
			# # topic reflection with remote_topic
			# #
			# # Value: String
			local_topic = "topic1"
			# # Need to subscribe to remote topics QoS.
			# #
			# # Value: Number
			qos = 1
		},
		{
			remote_topic = "cmd/topic2"
			local_topic = "topic2"
			qos = 2
		}
	]
	# # max_parallel_processes
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-infinity
	max_parallel_processes = 2
	# # Ssl config ##
	# # Ssl config is invalid when working in MQTT over QUIC mode ##
}


# #============================================================
# # MQTT Rule Engine
# #============================================================

rules.sqlite {
	# # Rule engine option SQLite3 database path
	# # Rule engine db path, default is exec path.
	# # 
	# # Value: File
	path = "/tmp/sqlite_rule.db"
	rules = [
		{
			# # Rule engine option sql
			# # Rule engine sql clause.
			# # 
			# # Value: String
			sql = "SELECT payload.x.y as y, payload.z as z FROM \"#\" WHERE y > 10 and z != 'str'"
			# # Rule engine option SQLite3 database table name
			# # Rule engine db table name.
			# # 
			# # Value: String
			table = broker
		},
		{
			sql = "SELECT topic, payload FROM \"abc\""
			table = broker1
		}
	]
}

# #====================================================================
# # MQTT Rule Engine for Repub
# #====================================================================
rules.repub {
	rules = [
		{
			# # Repub address: host:port .
			# #
			# # Value: String
			# # Example: mqtt-tcp://127.0.0.1:1883
			server = "mqtt-tcp://localhost:1883"
			# # Repub topic .
			# #
			# # Value: String
			# # Example: topic/repub
			topic = "topic/repub1"
			# # Protocol version of the Repub.
			# #
			# # Value: Enum
			# # - 5: mqttv5
			# # - 4: mqttv311
			# # - 3: mqttv31
			proto_ver = 4
			# # The ClientId of a Repub client.
			# # Default random string.
			# #
			# # Value: String
			clientid = "repub_client1"
			# # Ping interval of a Repub client.
			# #
			# # Value: Duration
			# # Default: 60 seconds
			keepalive = 60s
			# # The Clean start flag of a Repub client.
			# #
			# # Value: boolean
			# # Default: true
			# #
			# # NOTE: Some IoT platforms require clean_start
			# #       must be set to 'true'
			clean_start = true
			# # The username for a Repub client.
			# #
			# # Value: String
			username = username
			# # The password for a Repub.
			# #
			# # Value: String
			password = passwd
			# # Rule engine option sql
			# # Rule engine sql clause.
			# # 
			# # Value: String
			sql = "SELECT payload.x.y as y, payload.z as z FROM \"#\" WHERE y > 10 and z != 'str'"
		},
		{
			server = "mqtt-tcp://localhost:1883"
			topic = "topic/repub2"
			proto_ver = 4
			clientid = "repub_client2"
			keepalive = 60s
			clean_start = true
			username = username
			password = passwd
			sql = "SELECT topic, payload FROM \"abc\""
		}
	]
}

# #====================================================================
# # MQTT Rule Engine for Mysql
# #====================================================================
# # Currently, MySQL rule only supports the configuration of one database.
rules.mysql.mysql_rule_db {
	conn = {
		# # The host for a mqsql client.
		# #
		# # Value: String
		host = localhost
		# # The username for a mqsql client.
		# #
		# # Value: String
		username = username
		# # The password for a mysql client.
		# #
		# # Value: String
		password = password
		# # Rule engine option mysql database name
		# # Rule engine db path, default is exec path.
		# # 
		# # Value: File
		database = db_name
	}
	
	rules = [
		{
			# # Rule engine option mysql database table name
			# # Rule engine db table name.
			# # 
			# # Value: String
			table = broker
			# # Rule engine option sql
			# # Rule engine sql clause.
			# # 
			# # Value: String
			sql = "SELECT payload.x.y as y, payload.z as z FROM \"#\" WHERE y > 10 and z != 'str'"
		},
		{
			table = broker1
			sql = "SELECT * FROM \"abc\""
		}
	]
}

# #====================================================================
# # Exchange configuration for Embedded Messaging Queue
# #====================================================================
# # Initalize multiple MQ exchanger by giving them different name (mq1)
exchange_client.mq1 {
	# # Currently NanoMQ only support one MQ object. URL shall be exactly same.
	exchange_url = "tcp://127.0.0.1:10000"
	# # exchanges contains multiple MQ exchanger
	exchange {
		# # MQTT Topic for filtering messages and saving to queue
		topic = "exchange/topic1",
		# # MQ name
		name = "exchange_no1",
		# # MQ category. Only support Ringbus for now
		ringbus = {
			# # ring buffer name
			name = "ringbus",
			# # max length of ring buffer (msg count)
			cap = 1000,
			# #  0: RB_FULL_NONE: When the ringbus is full, no action is taken and the message enqueue fail
			# #  1: RB_FULL_DROP: When the ringbus is full, the data in the ringbus is discarded
			# #  2: RB_FULL_RETURN: When the ringbus is full, the data in the ringbus is taken out and returned to the aio
			# #  3: RB_FULL_FILE: When the ringbus is full, the data in the ringbus is written to the file
			#
			# # Value: 0-4
			# # Default: 0
			fullOp = 2
		}
	}
}


# #====================================================================
# # Parquet configuration (Apply to Exchange/Messaging_Queue)
# #====================================================================
parquet {
	# # Parquet compress type.
	# #
	# # Value: uncompressed | snappy | gzip | brotli | zstd | lz4
	compress = uncompressed
	# # Encryption options
	encryption {
		# # Set a key retrieval metadata.
		# #
		# # Value: String
		key_id = kf
		# # Parquet encryption key.
		# #
		# # Value: String key must be either 16, 24 or 32 bytes.
		key = "0123456789012345"
		# # Set encryption algorithm. If not called, files 
		# # will be encrypted with AES_GCM_V1 (default).
		# #
		# # Value: AES_GCM_CTR_V1 | AES_GCM_V1
		type = AES_GCM_V1
	}
	# # The dir for parquet files.
	# #
	# # Value: Folder
	dir = "/tmp/nanomq-parquet"
	# # The prefix of parquet files written.
	# #
	# # Value: string
	file_name_prefix = ""
	# # Maximum rotation count of parquet files.
	# #
	# # Value: Number
	# # Default: 5
	file_count = 5
	# # The max size of parquet file written.
	# #
	# # Default: 10M
	# # Value: Number
    # # Supported Unit: KB | MB | GB
    file_size = 1KB
	# # The max number of searches per second.
	# #
	# # Default: 5
	# # Value: Number
    limit_frequency = 5
}

plugin {
    libs = [{
        path = "/path/to/plugin_user_property.so"
    }]
}
