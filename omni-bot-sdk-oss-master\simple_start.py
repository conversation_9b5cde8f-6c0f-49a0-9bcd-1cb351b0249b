#!/usr/bin/env python3
"""
简化版微信机器人启动脚本
绕过有问题的二进制模块，使用基础功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试各个模块的导入"""
    print("🔍 测试模块导入...")
    
    try:
        import omni_bot_sdk
        print("✅ omni_bot_sdk 基础模块导入成功")
    except Exception as e:
        print(f"❌ omni_bot_sdk 导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.common.config import Config
        print("✅ 配置模块导入成功")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.rpa.controller import RPAController
        print("✅ RPA控制器导入成功")
    except Exception as e:
        print(f"❌ RPA控制器导入失败: {e}")
        return False
    
    try:
        from omni_bot_sdk.plugins.plugin_manager import PluginManager
        print("✅ 插件管理器导入成功")
    except Exception as e:
        print(f"❌ 插件管理器导入失败: {e}")
        return False
    
    # 测试有问题的模块
    try:
        from omni_bot_sdk.services.core.database_service import DatabaseService
        print("✅ 数据库服务导入成功")
    except Exception as e:
        print(f"⚠️  数据库服务导入失败: {e}")
        print("   这是已知问题，需要重新编译或使用替代方案")
        return False
    
    return True

def check_config():
    """检查配置文件"""
    config_path = project_root / "config.yaml"
    if not config_path.exists():
        print("❌ 配置文件 config.yaml 不存在")
        return False
    
    print("✅ 配置文件存在")
    return True

def main():
    print("🤖 微信机器人诊断工具")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major != 3 or python_version.minor != 12:
        print("⚠️  警告: 推荐使用Python 3.12")
    
    # 检查配置
    if not check_config():
        print("请先创建配置文件")
        return
    
    # 测试导入
    if test_imports():
        print("\n🎉 所有模块导入成功！")
        print("现在可以尝试启动完整的机器人")
        
        try:
            from omni_bot_sdk.bot import Bot
            print("正在启动机器人...")
            bot = Bot(config_path="config.yaml")
            bot.start()
        except Exception as e:
            print(f"启动失败: {e}")
    else:
        print("\n❌ 存在模块导入问题")
        print("建议:")
        print("1. 检查Python版本是否为3.12")
        print("2. 重新安装omni-bot-sdk")
        print("3. 联系项目维护者获取兼容的二进制文件")

if __name__ == "__main__":
    main()
