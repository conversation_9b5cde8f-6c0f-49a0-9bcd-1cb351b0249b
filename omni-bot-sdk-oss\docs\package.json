{"name": "omni-bot-docs", "version": "0.1.0", "private": true, "author": "Huchundong <<EMAIL>>", "license": "CC0-1.0", "repository": {"type": "git", "url": "git+ssh://**************/weixin-omni/omni-bot-sdk-oss.git"}, "bugs": {"url": "https://github.com/weixin-omni/omni-bot-sdk-oss/issues"}, "homepage": "https://github.com/weixin-omni/omni-bot-sdk-oss#readme", "packageManager": "pnpm@10.12.4", "scripts": {"dev": "vitepress dev", "build": "vitepress build", "serve": "vitepress serve", "fmt": "prettier --write .", "fmt:check": "prettier --check ."}, "devDependencies": {"@moefy-canvas/core": "^0.6.0", "@moefy-canvas/theme-sparkler": "^0.6.0", "mermaid": "^11.8.1", "prettier": "^3.5.2", "vite": "^7.0.0", "vitepress": "^1.6.3", "vitepress-plugin-group-icons": "^1.3.6", "vitepress-plugin-llms": "^1.0.0", "vitepress-plugin-mermaid": "^2.0.17", "vue": "^3.5.13"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@algolia/client-search", "react", "react-dom", "@types/react"]}}}