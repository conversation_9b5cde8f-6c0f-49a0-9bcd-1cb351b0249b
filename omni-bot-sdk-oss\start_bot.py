#!/usr/bin/env python3
"""
微信机器人启动脚本
"""

import sys
import os
from pathlib import Path

def main():
    print("🤖 启动微信机器人...")
    print("=" * 50)
    
    # 检查配置文件
    config_path = Path("config.yaml")
    if not config_path.exists():
        print("❌ 配置文件 config.yaml 不存在")
        input("按回车键退出...")
        return
    
    print("✅ 配置文件存在")
    
    try:
        # 导入并启动机器人
        from omni_bot_sdk.bot import Bot
        print("✅ omni-bot-sdk 导入成功")
        
        print("🚀 正在启动机器人...")
        print("⚠️  重要提醒：")
        print("   - RPA初始化需要扫描窗口并定位，请勿在此期间操作电脑")
        print("   - 微信窗口将会自动缩放并移动到屏幕左上角位置")
        print("   - 运行时请勿操作鼠标键盘，以免影响RPA运行")
        print()
        
        bot = Bot(config_path="config.yaml")
        bot.start()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n可能的解决方案：")
        print("1. 确保微信已登录")
        print("2. 确保数据库密钥正确")
        print("3. 检查MQTT服务是否启动")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
