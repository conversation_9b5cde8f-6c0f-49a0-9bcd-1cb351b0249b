dbkey: ""  # 需要填入您获取的数据库密钥

# AES加密用的密钥和XOR，格式为字符串, 设置为空，自动查找
aes_xor_key: 

# MCP服务配置
mcp:
  host: 0.0.0.0
  port: 8000

dingtalk:
  # 钉钉机器人Webhook地址，当微信异常时，将推送异常消息和登录二维码
  webhook_url: ""

logging:
  backup_count: 5
  level: INFO
  max_size: 10485760
  path: logs

mqtt:
  client_id: weixin_omni
  host: 127.0.0.1
  password: ""
  port: 1883
  username: weixin

plugins:
  # 空群屏蔽插件
  block-empty-room-plugin:
    enabled: true
    priority: 2000
  # 聊天上下文插件
  chat-context-plugin:
    enabled: false
    priority: 1999
  # OpenAI对话插件
  openai-bot-plugin:
    enabled: false
    priority: 1497
    openai_api_key: ""
    openai_base_url: ""
    openai_model: "gpt-3.5-turbo"
    prompt: "你是一个聊天机器人，请根据用户的问题给出回答。"
  # 自发消息插件
  self-msg-plugin:
    enabled: true
    priority: 1998

rpa:
  action_delay: 0.3
  scroll_delay: 1
  max_retries: 3
  switch_contact_delay: 0.3
  ocr:
    merge_threshold: 5.0
    min_confidence: 0.5
    remote_url: ""
    use_remote: false
  room_action_offset:
  - 0
  - -30
  search_contact_offset:
  - 0
  - 40
  side_bar_delay: 3
  timeout: 30
  window_margin: 20
  window_show_delay: 1.5
  short_term_rate: 0.15
  short_term_capacity: 2
  long_term_rate: 0.25
  long_term_capacity: 10

wxgf:
  api_url: ""

s3:
  endpoint_url: ""
  access_key: ""
  secret_key: ""
  region: ""
  bucket: ""
  public_url_prefix: ""
