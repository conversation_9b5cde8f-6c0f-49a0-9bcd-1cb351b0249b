# Example1. (default)
# This configure will make msgs with topic topic1 be shared in DDS network and MQTT network.
# It receive messages from MQTT network in topic DDSCMD/topic1 and send them to DDS network with topic MQTT/topic1.
# It receive messages from DDS network in topic MQTTCMD/topic1 and send them to MQTT network with topic DDS/topic1.

forward_rules = {
    dds_to_mqtt = [
        {
            from_dds = "MQTTCMD/topic1"
            to_mqtt = "DDS/topic1"
            struct_name = "example_struct"
        }
        {
            from_dds = "MQTTCMD/topic2"
            to_mqtt = "DDS/topic2"
            struct_name = "example_struct"
        }
    ],

    mqtt_to_dds = [
        {
            from_mqtt = "DDSCMD/topic1"
            to_dds = "MQTT/topic1"
            struct_name = "example_struct"
        }
        {
            from_mqtt = "DDSCMD/topic2"
            to_dds = "MQTT/topic2"
            struct_name = "example_struct"
        }
    ]
}

dds {
    # # dds domain id
    # # default: 0
    # # Value: uint32
    domain_id = 0

    # # dds subscriber partition. Setting multiple partitions is not supported yet.
    # # default: "partition"
    # # Value: string
    subscriber_partition = "partition"

    # # dds publisher partition. Setting multiple partitions is not supported yet.
    # # default: "partition"
    # # Value: string
    publisher_partition = "partition"

    shared_memory = {
        # # Enable shared memory transport.
        # # Iceoryx is required if enable shared memory transport.
        # #
        # # Default: false
        # # Value:  boolean
        enable = false

        # # controls the output of the iceoryx runtime and can be set to, in order of decreasing output:
        # # log level: verbose, debug, info, warn, error, fatal, off
        # # Default:  info
        # # Value: enum
        log_level = info
    }
}

mqtt {
	connector {
        # # Bridge address: host:port .
        # #
        # # Value: String
        # # Example: mqtt-tcp://127.0.0.1:1883
        # #          tls+mqtt-tcp://127.0.0.1:8883
        server = "mqtt-tcp://127.0.0.1:1883"

        # # Protocol version of the bridge.
        # #
        # # Value: Enum
        # # - 5: mqttv5
        # # - 4: mqttv311
        # # - 3: mqttv31
        proto_ver = 4
        # # The ClientId of a remote bridge.
        # # Default random string.
        # #
        # # Value: String
        # clientid="bridge_client"

        # # Ping: interval of a downward bridge.
        # #
        # # Value: Duration
        # # Default: 10 seconds
        keepalive = 60s
        # # The Clean start flag of a remote bridge.
        # #
        # # Value: boolean
        # # Default: false
        # #
        # # NOTE: Some IoT platforms require clean_start
        # #       must be set to 'true'
        clean_start = true
        # # The username for a remote bridge.
        # #
        # # Value: String
        username = username
        # # The password for a remote bridge.
        # #
        # # Value: String
        password = passwd

        // ssl {
        //     # # Ssl key password
        //     # # String containing the user's password. Only used if the private keyfile
        //     # # is password-protected.
        //     # #
        //     # # Value: String
        //     key_password = "yourpass"
        //     # # Ssl keyfile
        //     # # Path of the file containing the client's private key.
        //     # #
        //     # # Value: File
        //     keyfile = "/etc/certs/key.pem"
        //     # # Ssl cert file
        //     # # Path of the file containing the client certificate.
        //     # #
        //     # # Value: File
        //     certfile = "/etc/certs/cert.pem"
        //     # # Ssl ca cert file
        //     # # Path of the file containing the server's root CA certificate.
        //     # #
        //     # # This certificate is used to identify the AWS IoT server and is publicly
        //     # # available.
        //     # #
        //     # # Value: File
        //     cacertfile = "/etc/certs/cacert.pem"
        // }
    }
}

# #============================================================
# # Http server
# #============================================================
http_server {
	# # http server port
	# #
	# # Value: 0 - 65535
	port = 8082
	# # parallel for http server
	# # Handle a specified maximum number of outstanding requests
	# #
	# # Value: 1-infinity
	parallel = 2
	# # username
	# #
    # # Basic authorization
    # #
	# # Value: String
	username = admin
	# # password
	# #
    # # Basic authorization
    # #
	# # Value: String
	password = public
}
