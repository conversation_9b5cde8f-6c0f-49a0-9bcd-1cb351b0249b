# NanoMQ Configuration 0.19.0

# #============================================================
# # NanoMQ Broker
# #============================================================

mqtt {
    property_size = 32
    max_packet_size = 256MB
    max_mqueue_len = 2048
    retry_interval = 10s
    keepalive_multiplier = 1.25
    
    # Three of below, unsupported now
    max_inflight_window = 2048
    max_awaiting_rel = 10s
    await_rel_timeout = 10s
}

listeners.tcp {
    bind = "0.0.0.0:1884"
}

# listeners.ssl {
# 	bind = "0.0.0.0:8883"
# 	keyfile = "/etc/certs/key.pem"
# 	certfile = "/etc/certs/cert.pem"
# 	cacertfile = "/etc/certs/cacert.pem"
# 	verify_peer = false
# 	fail_if_no_peer_cert = false
# }

listeners.ws {
    bind = "0.0.0.0:8083/mqtt"
}

http_server {
    port = 8081
    limit_conn = 2
    username = admin
    password = public
    auth_type = basic
    jwt {
        public.keyfile = "/etc/certs/jwt/jwtRS256.key.pub"
    }
}

log {
    to = [file, console]
    level = warn
    dir = "/tmp"
    file = "nanomq.log"
    rotation {
        size = 10MB
        count = 5
    }
}

auth {
    allow_anonymous = true
    no_match = allow
    deny_action = ignore
    
    cache = {
        max_size = 32
        ttl = 1m
    }
    
    # password = {include "/etc/nanomq_pwd.conf"}
    # acl = {include "/etc/nanomq_acl.conf"}
}
